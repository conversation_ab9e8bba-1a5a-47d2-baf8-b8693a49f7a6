package com.ssbms.backOfficeService.utils;

import java.security.SecureRandom;

public final class ApiKeyGenerator {
    private static final SecureRandom SECURE_RANDOM = new SecureRandom();
    private static final int PREFIX_LENGTH = 10;
    private static final int KEY_LENGTH = 40;
    private static final String CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_";

    private ApiKeyGenerator() { }

    public static String generatePrefix() {
        return generateRandomString(CHARS, PREFIX_LENGTH);
    }

    public static String generateMainKey() {
        return generateRandomString(CHARS, KEY_LENGTH);
    }

    private static String generateRandomString(String characterSet, int length) {
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int index = SECURE_RANDOM.nextInt(characterSet.length());
            sb.append(characterSet.charAt(index));
        }
        return sb.toString();
    }
}