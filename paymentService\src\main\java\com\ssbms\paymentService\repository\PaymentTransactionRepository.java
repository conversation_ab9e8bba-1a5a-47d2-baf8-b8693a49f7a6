package com.ssbms.paymentService.repository;

import com.ssbms.paymentService.model.entity.PaymentTransaction;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface PaymentTransactionRepository extends JpaRepository<PaymentTransaction, UUID> {

    @Query("SELECT pt FROM PaymentTransaction pt " +
            "JOIN pt.paymentMethod pm "+
            "JOIN pm.endUser eu "+
            "WHERE eu.email = :email "+
            "ORDER BY pt.createdAt DESC")
    List<PaymentTransaction> findAllByEndUserEmail(@Param("email") String email);
}
