package com.ssbms.authService.model.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Data
@Table(name = "billing_cycles")
public class BillingCycle {

    @Id
    @GeneratedValue
    private UUID id;

    // Relationship with Subscription
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    @JoinColumn(name = "subscription_id", nullable = false)
    private EndUserSubscription subscription;

    // Relationship with Payment Method
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    @JoinColumn(name = "payment_method_id")
    private PaymentMethod paymentMethod;

    // Relationship with Payment Transactions
    @OneToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    @JoinColumn(name = "transaction_id")
    private PaymentTransaction paymentTransaction;

    @Column(name = "billing_date")
    private LocalDateTime billingDate;

    @Column(name = "base_amount")
    private Double baseAmount;

    @Column(name = "proration_amount")
    private Double prorationAmount;

    @Column(name = "discount_amount")
    private Double discountAmount;

    @Column(name = "tax_amount")
    private Double taxAmount;

    @Column(name = "total_amount")
    private Double totalAmount;

    @Column(name = "currency_code")
    private String currencyCode;

    @Column(name = "status")
    private String status; // PENDING, PAID, PARTIALLY_PAID, FAILED, VOIDED, PROCESSING

    @Column(name = "error_message")
    private String errorMessage;

    @Column(name = "description")
    private String description;

    @Column(name = "metadata")
    private String metadata; // JSONB field

    @Column(name = "retry_count")
    private int retryCount;

    @CreationTimestamp
    @JsonIgnore
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @JsonIgnore
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}