package com.ssbms.backOfficeService.service;

import com.ssbms.backOfficeService.model.dto.ProjectSettings.ManageProjectSettingsResponse;
import com.ssbms.backOfficeService.model.dto.ProjectSettings.ProjectSettingRequest;
import com.ssbms.backOfficeService.model.dto.projectDTO.*;

import java.util.List;
import java.util.UUID;

public interface ProjectService {

    ProjectResponse createProject(ProjectRequest projectDTO);

    List<ProjectRequest> getProjectsByBusinessClientId();

    // BusinessClient getBusinessClientIdByProjectId(UUID projectId);

    String manageProjectSettings(UUID projectID, List<ProjectSettingRequest> settings);

    // ********************** public functions *****************************
    ProjectResponse createProjectPublic(UUID businessClientId, ProjectRequest projectDTO);

    List<ProjectRequest> getProjectsByBusinessClientIdPublic(UUID businessClientId);

    List<ManageProjectSettingsResponse> getAllProjectSettings(UUID projectId);

    void updateProjectEnvironment(UUID project, UpdateEnvironmentRequest request);
}
