package com.ssbms.paymentService.model.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Entity
@Table(name = "failed_payments")
public class FailedPayment {

    @Id
    @GeneratedValue
    private UUID id;

    @Column(name = "reference", unique = true)
    private String reference;

    @Column(name = "payment_method_id")
    private UUID paymentMethodId;

    @Column(name = "amount", nullable = false)
    private Double amount;

    @Column(name = "currency", nullable = false)
    private String currency;

    @Column(name = "failure_date", nullable = false)
    private LocalDateTime failureDate;

    @Column(name = "failure_reason", nullable = false)
    private String failureReason;

    @Column(name = "failure_code")
    private String failureCode;

    @Column(name = "gateway_error_message")
    private String gatewayErrorMessage;

    @Column(name = "retry_status")
    private String retryStatus;

    @Column(name = "retry_count")
    private Integer retryCount;

    @Column(name = "next_retry_date")
    private LocalDateTime nextRetryDate;

    @Column(name = "last_retry_date")
    private LocalDateTime lastRetryDate;

    @Column(name = "external_transaction_id")
    private String externalTransactionId;

    // failed payment -> billing cycle
    @OneToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    @JoinColumn(name = "billing_cycle_id", referencedColumnName = "id")
    private BillingCycle billingCycle;

    @CreationTimestamp
    @JsonIgnore
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @JsonIgnore
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}
