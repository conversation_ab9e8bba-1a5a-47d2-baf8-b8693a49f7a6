package com.ssbms.authService.model.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class RegisterRequest {
    @NotBlank(message = "First name is required")
    @Size(min = 3, max = 20, message = "First Name must be 3-20 characters")
    private String firstName;

    @NotBlank(message = "Last name is required")
    @Size(min = 3, max = 20, message = "Last Name must be 3-20 characters")
    private String lastName;

    @NotBlank(message = "Username is required")
    @Email(regexp = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$", message = "Invalid email format (e.g., <EMAIL>)")
    private String username;

    @NotBlank(message = "Password is required")
    @Size(min = 8, max = 20, message = "Password must be 8-20 characters")
    private String password;

//    private String businessName;
//    private String businessType;
//    private String industry;
//    private String companySize;
//    private String primaryContactName;
//    private String contactEmail;
//    private String contactPhone;
//    private String addressLine1;
//    private String addressLine2;
//    private String city;
//    private String stateProvince;
//    private String postalCode;
//    private String country;
//    private String timezone;
//    private String locale;
//    private Boolean marketingEmailsOptIn;
//    private Boolean invoiceNotificationEmails;
//    private Boolean paymentReminderEmails;
//    private Boolean serviceUpdateEmails;
//    private Boolean emailNotifications;
//    private Boolean smsNotifications;
//    private String referralSource;
//    private String notes;
}
