package com.ssbms.notificationService.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ssbms.notificationService.exception.NotFoundException;
import com.ssbms.notificationService.model.entity.*;
import com.ssbms.notificationService.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class NotificationServiceImpl {

    private final JavaMailSender mailSender;
    private final ObjectMapper objectMapper;
    private final PaymentTransactionRepository paymentTransactionRepository;

    @KafkaListener(topics = "notifications")
    public void sendNotification(String jsonPayload) {
        try {
            JsonNode root = objectMapper.readTree(jsonPayload);
            String type = root.path("type").asText();
            UUID transactionId = UUID.fromString(root.path("transactionId").asText());

            // Add retry mechanism
            PaymentTransaction transaction = null;
            int retries = 3;
            long delay = 1000; // 1 second

            for (int i = 0; i < retries; i++) {
                try {
                    transaction = paymentTransactionRepository.findTransactionForNotification(transactionId)
                            .orElseThrow(() -> new NotFoundException("Transaction not found: " + transactionId));
                    break;
                } catch (NotFoundException e) {
                    if (i == retries - 1) throw e;
                    Thread.sleep(delay);
                    delay *= 2; // exponential backoff
                }
            }

            if (transaction.getPaymentMethod() == null || transaction.getPaymentMethod().getEndUser() == null) {
                log.error("Incomplete data for transaction {}", transactionId);
                return;
            }

            switch (type) {
                case "PAYMENT_CONFIRMATION" -> sendPaymentConfirmationEmail(transaction);
                case "WELCOME_EMAIL" -> sendWelcomeEmail(transaction);
                default -> log.warn("Unknown notification type: {}", type);
            }
        } catch (Exception e) {
            log.error("Failed to process notification", e);
        }
    }

    private void sendPaymentConfirmationEmail(PaymentTransaction transaction) {
        try {
            EndUser endUser = transaction.getPaymentMethod().getEndUser();
            List<EndUserSubscription> subscriptions = endUser.getEndUserSubscriptions();

            if (subscriptions == null || subscriptions.isEmpty()) {
                log.error("No subscriptions found for user {}", endUser.getId());
                return;
            }

            EndUserSubscription subscription = subscriptions.get(0);
            String subscriptionPlan = subscription.getSystemSubscriptionPlan() != null ?
                    subscription.getSystemSubscriptionPlan() : "Unknown Plan";

            SimpleMailMessage emailMessage = new SimpleMailMessage();
            emailMessage.setTo(endUser.getEmail());
            emailMessage.setSubject("Payment Confirmation");
            emailMessage.setText(String.format(
                    "Dear %s,\n\n" +
                            "We have received your payment of %.2f %s.\n" +
                            "Reference: %s\n" +
                            "Card: **** **** **** %s\n" +
                            "Date: %s\n\n" +
                            "Subscription: %s\n\n" +
                            "Thank you for your trust.\n\n" +
                            "Best regards,\n" +
                            "Customer Support Team",
                    endUser.getUsername(),
                    transaction.getAmount(),
                    transaction.getCurrency(),
                    transaction.getReference(),
                    transaction.getCardLastFour(),
                    transaction.getProcessedAt().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")),
                    subscriptionPlan
            ));

            mailSender.send(emailMessage);
            log.info("Payment confirmation email sent to: {}", endUser.getEmail());
        } catch (Exception e) {
            log.error("Failed to send payment confirmation email", e);
        }
    }

    private void sendWelcomeEmail(PaymentTransaction transaction) {
        try {
            EndUser endUser = transaction.getPaymentMethod().getEndUser();
            List<EndUserSubscription> subscriptions = endUser.getEndUserSubscriptions();

            if (subscriptions == null || subscriptions.isEmpty()) {
                log.error("No subscriptions found for user {}", endUser.getId());
                return;
            }

            EndUserSubscription subscription = subscriptions.get(0);
            String subscriptionPlan = subscription.getSystemSubscriptionPlan() != null ?
                    subscription.getSystemSubscriptionPlan() : "Unknown Plan";

            SimpleMailMessage emailMessage = new SimpleMailMessage();
            emailMessage.setTo(endUser.getEmail());
            emailMessage.setSubject("Welcome to Our Platform");
            emailMessage.setText(String.format(
                    "Hello %s,\n\n" +
                            "Welcome to our platform! Your account has been successfully created.\n\n" +
                            "Your first payment of %.2f %s has been processed successfully.\n" +
                            "Reference: %s\n" +
                            "Card: **** **** **** %s\n" +
                            "Date: %s\n\n" +
                            "Subscription: %s\n\n" +
                            "For any questions, our support team is at your disposal.\n\n" +
                            "Best regards,\n" +
                            "Customer Support Team",
                    endUser.getUsername(),
                    transaction.getAmount(),
                    transaction.getCurrency(),
                    transaction.getReference(),
                    transaction.getCardLastFour(),
                    transaction.getProcessedAt().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")),
                    subscriptionPlan
            ));

            mailSender.send(emailMessage);
            log.info("Welcome email sent to: {}", endUser.getEmail());
        } catch (Exception e) {
            log.error("Failed to send welcome email", e);
        }
    }
}


