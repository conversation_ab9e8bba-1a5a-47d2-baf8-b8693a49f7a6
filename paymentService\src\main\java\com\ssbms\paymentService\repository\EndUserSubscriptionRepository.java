package com.ssbms.paymentService.repository;

import com.ssbms.paymentService.model.entity.EndUserSubscription;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface EndUserSubscriptionRepository extends JpaRepository<EndUserSubscription, UUID> {

    Optional<EndUserSubscription> findByEndUser_Id(UUID userId);

    List<EndUserSubscription> findByStatus(String status); // Fetch active subscriptions

    Optional<EndUserSubscription> findByReference(String idcommande);
}
