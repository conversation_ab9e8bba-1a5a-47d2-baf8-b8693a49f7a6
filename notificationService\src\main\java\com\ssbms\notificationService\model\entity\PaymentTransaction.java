package com.ssbms.notificationService.model.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Entity
@Table(name = "payment_transactions")
public class PaymentTransaction {

    @GeneratedValue
    @Id
    private UUID id;

    @Column(name = "reference")
    private String reference;

    // Relationship with Payment Method
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    @JoinColumn(name = "payment_method_id")
    private PaymentMethod paymentMethod;

    // Relationship with Billing Cycle
    @OneToOne(mappedBy = "paymentTransaction", fetch = FetchType.LAZY)
    @JsonIgnore
    private BillingCycle billingCycle;

    @Column(name = "transaction_reference", nullable = false)
    private String transactionReference;

    @Column(name = "amount", nullable = false)
    private Double amount;

    @Column(name = "currency", nullable = false)
    private String currency;

    @Column(name = "status", nullable = false)
    private String status;

    @Column(name = "payment_processor")
    private String paymentProcessor;

    @Column(name = "last_4_digits", nullable = false)
    private String cardLastFour;

    @Column(name = "error_message")
    private String errorMessage;

    @Column(name = "processed_at", nullable = false)
    private LocalDateTime processedAt;

    @Column(name = "external_transaction_id")
    private String externalTransactionId;

    @CreationTimestamp
    @JsonIgnore
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @JsonIgnore
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}
