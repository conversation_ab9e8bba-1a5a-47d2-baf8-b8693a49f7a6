spring:
  application:
    name: notification-service

  # PostgreSQL database configuration
  datasource:
    url: ${SPRING_DATASOURCE_URL:*****************************************}
    username: ssbms
    password: ssbms1234
    driver-class-name: org.postgresql.Driver

  # JPA (Hibernate) configuration
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    properties:
      hibernate:
        allow-circular-references: true

  # Kafka configuration
  kafka:
    bootstrap-servers: ${SPRING_KAFKA_BOOTSTRAP_SERVERS:localhost:2025}

    consumer:
      group-id: notification-consumers
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
#      properties:
#        spring.json.trusted.packages: "com.ssbms.notificationService.model.dto"
#        spring.json.value.default.type: com.ssbms.notificationService.model.dto.NotificationRequest

    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer

  # Mail configuration (for notifications)
  mail:
    host: smtp.gmail.com
    port: 587
    username: <EMAIL>
    password: zgpp bgau yucd dqpd
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
          ssl:
            trust: "*" # Trust all certificates (not recommended for production)

# Swagger (OpenAPI) configuration
springdoc:
  swagger-ui:
    path: /swagger-ui.html
  api-docs:
    path: /api-docs

server:
  port: ${SERVER_PORT:1003}