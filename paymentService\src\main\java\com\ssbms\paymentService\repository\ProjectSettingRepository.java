package com.ssbms.paymentService.repository;

import com.ssbms.paymentService.model.entity.ProjectSetting;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface ProjectSettingRepository extends JpaRepository<ProjectSetting, UUID> {

    Optional<ProjectSetting> findByProjectIdAndKey(UUID projectId, String key);

    List<ProjectSetting> findByProjectId(UUID projectId);
}
