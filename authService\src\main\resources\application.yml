# Application configuration
spring:
  application:
    name: auth-service

  # OAuth2 Security configuration
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${KEYCLOAK_ISSUER_URI:http://localhost:9082/realms/${app.keycloak.realm}}

  # PostgreSQL database configuration
  datasource:
    url: ${SPRING_DATASOURCE_URL:*****************************************}
    username: ssbms
    password: ssbms1234
    driver-class-name: org.postgresql.Driver

  # JPA (Hibernate) configuration
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    properties:
      hibernate:
        allow-circular-references: true

server:
  port: ${SERVER_PORT:1000}

# Keycloak configuration
app:
  keycloak:
    admin:
      clientId: admin-cli
      clientSecret: tFq10IxAwM2zyPwO6Jq1eAj1byFbWL43
    realm: saas
    serverUrl: ${KEYCLOAK_SERVER_URL:http://localhost:9082}

# Swagger (OpenAPI) configuration
springdoc:
  swagger-ui:
    path: /swagger-ui.html
  api-docs:
    path: /api-docs

logoutKeycloakUrl: ${app.keycloak.serverUrl} + "/realms/" + ${app.keycloak.realm} + "/protocol/openid-connect/logout";

# Logging configuration
logging:
  level:
    org:
      springframework:
        security: DEBUG
        web.client.RestTemplate: DEBUG
    org.apache.tomcat.util.net: DEBUG
    org.springframework.boot.web.embedded.tomcat: DEBUG
    org.springframework.web.reactive.function.client: DEBUG
