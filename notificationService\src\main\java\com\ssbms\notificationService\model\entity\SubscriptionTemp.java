package com.ssbms.notificationService.model.entity;

import com.ssbms.notificationService.model.enums.BillingInterval;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Table(name = "SUB_TMP")
@Getter
@Setter
@NoArgsConstructor
public class SubscriptionTemp {
    @Id
    private String reference;

    private UUID businessClientId;
    private UUID projectId;

    // User Data
    private String userExternalId;
    private String userEmail;
    private String userUsername;
    private String userAuthProvider;

    // Subscription Plan
    private String subscriptionExternalId;
    private String systemSubscriptionPlan;
    private LocalDateTime subscriptionStartDate;
    private LocalDateTime subscriptionEndDate;
    private Double subscriptionPrice;
    @Enumerated(EnumType.STRING)
    @Column(name = "billing_interval")
    private BillingInterval billingInterval;

    // Payment Method
    private String paymentToken;
    private String paymentCurrency;

    // Metadata
    private LocalDateTime createdAt;
    private String status;

    @Column(name = "success_url")
    private String successUrl;
    @Column(name = "failure_url")
    private String failureUrl;
    @Column(name = "base_url")
    private String baseUrl;
}
