package com.ssbms.paymentService.model.dto.payment;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class WalletApiRequest {
    private String currency;
    private String itemid;
    private String merchantid;
    private Double amount;
    private String merchantname;
    private String websitename;
    private String websiteid;
    private String callbackurl;
    private String fname;
    private String lname;
    private String email;
    private String country;
    private String city;
    private String state;
    private String zipcode;
    private String address;
    private String phone;
    private String id_client;
    private String token;
}
