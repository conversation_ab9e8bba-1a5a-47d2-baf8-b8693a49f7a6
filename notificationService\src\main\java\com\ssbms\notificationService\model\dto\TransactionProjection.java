package com.ssbms.notificationService.model.dto;

import com.ssbms.notificationService.model.entity.PaymentMethod;

import java.time.LocalDateTime;
import java.util.UUID;

public interface TransactionProjection {
    UUID getId();
    String getReference();
    Double getAmount();
    String getCurrency();
    String getCardLastFour();
    LocalDateTime getProcessedAt();
    PaymentMethod getPaymentMethod();
}
