package com.ssbms.authService.model.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ssbms.authService.model.enums.BillingInterval;
import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Entity
@Table(name = "end_user_subscriptions")
public class EndUserSubscription {

    @Id
    @GeneratedValue
    private UUID id;

    @Column(name = "reference", unique = true)
    private String reference;

    // Relationship with End User
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    @JoinColumn(name = "end_user_id", nullable = false)
    private EndUser endUser;

    @Column(name = "system_subscription_plan")
    private String systemSubscriptionPlan;

    @CreationTimestamp
    @Column(name = "start_date")
    private LocalDateTime startDate;

    @Column(name = "end_date")
    private LocalDateTime endDate;

    @Column(name = "status")
    private String status;

    @Column(name = "price")
    private Double price;

    @Column(name = "external_id")
    private String externalId;

    @Enumerated(EnumType.STRING)
    @Column(name = "billing_interval")
    private BillingInterval billingInterval;

    @CreationTimestamp
    @JsonIgnore
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @JsonIgnore
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}
