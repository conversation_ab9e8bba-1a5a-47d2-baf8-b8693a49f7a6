package com.ssbms.paymentService.model.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Table(name = "invoices")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Invoice {

	@Id
	@GeneratedValue(strategy = GenerationType.UUID)
	private UUID id;

    @ManyToOne
    @JoinColumn(name = "transaction_id")
	private PaymentTransaction transaction;

	@Column(name = "invoice_number", nullable = false)
	private String invoiceNumber;

	@Column(name = "created_at", nullable = false)
	private LocalDateTime createdAt;

	@Column(name = "pdf_path")
	private String pdfPath;

	@Column(name = "status")
	private String status; // GENERATED, SENT, etc.

	@PrePersist
	public void prePersist() {
		createdAt = LocalDateTime.now();
	}
}
