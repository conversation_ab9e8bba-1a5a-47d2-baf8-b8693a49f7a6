version: "3"
services:
  postgres:
    container_name: postgres
    image: postgres:latest
    restart: always
    ports:
      - "5432:5432"
    volumes:
      - ./pgdata:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    environment:
      POSTGRES_DB: ssbms_db
      POSTGRES_USER: ssbms
      POSTGRES_PASSWORD: ssbms1234
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ssbms -d ssbms_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - local
      - ssbms-network

  pgadmin:
    container_name: pgadmin
    image: dpage/pgadmin4
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=root
    volumes:
      - pgadmin:/var/lib/pgdata
    ports:
      - "5050:80"
    networks:
      - local
      - ssbms-network

  keycloak:
    container_name: keycloak
    image: quay.io/keycloak/keycloak:25.0.0
    command: start --https-certificate-file=/opt/keycloak/conf/keycloak.crt --https-certificate-key-file=/opt/keycloak/conf/keycloak.key --hostname-strict=false
    restart: always
    depends_on:
      postgres:
        condition: service_healthy
    ports:
      - "9082:8080" # HTTP port
      - "9443:8443" # HTTPS port
    volumes:
      - ./ssl/keycloak/keycloak.crt:/opt/keycloak/conf/keycloak.crt:ro
      - ./ssl/keycloak/keycloak.key:/opt/keycloak/conf/keycloak.key:ro
      - ./ssl/rootCA.crt:/opt/keycloak/conf/rootCA.crt:ro
      - ./ssl/keycloak/truststore.p12:/opt/keycloak/conf/truststore.p12:ro
      - ./ssl/spring-app/spring-app.crt:/opt/keycloak/conf/trusted-certs/spring-app.crt:ro
    environment:
      KEYCLOAK_ADMIN: admin
      KEYCLOAK_ADMIN_PASSWORD: admin1234
      KC_DB: postgres
      KC_DB_URL: ****************************************
      KC_DB_USERNAME: ssbms
      KC_DB_PASSWORD: ssbms1234
      KC_DB_SCHEMA: public
      KC_HOSTNAME: localhost
      KC_HOSTNAME_STRICT: "false"
      KC_HOSTNAME_STRICT_HTTPS: "false"
      KC_HTTPS_PROTOCOLS: "TLSv1.3,TLSv1.2"
      KC_HTTP_ENABLED: "true"
      KC_HTTPS_CERTIFICATE_FILE: /opt/keycloak/conf/keycloak.crt
      KC_HTTPS_CERTIFICATE_KEY_FILE: /opt/keycloak/conf/keycloak.key
      KC_PROXY: edge
      KC_LOG_LEVEL: INFO
      # Email Configuration
      KC_SPI_EMAIL_DEFAULT_FROM: <EMAIL>
      KC_SPI_EMAIL_DEFAULT_FROM_DISPLAY_NAME: "Your App Name"
      KC_SPI_EMAIL_DEFAULT_REPLY_TO: <EMAIL>
      KC_SPI_EMAIL_DEFAULT_SMTP_HOST: smtp.gmail.com
      KC_SPI_EMAIL_DEFAULT_SMTP_PORT: 587
      KC_SPI_EMAIL_DEFAULT_SMTP_AUTH: "true"
      KC_SPI_EMAIL_DEFAULT_SMTP_STARTTLS: "true"
      KC_SPI_EMAIL_DEFAULT_SMTP_USER: <EMAIL>
      KC_SPI_EMAIL_DEFAULT_SMTP_PASSWORD: zgpp bgau yucd dqpd
    networks:
      - local
      - ssbms-network

  # kafka for subscriptions
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    container_name: zookeeper-subscriptions
    environment:
      ZOOKEEPER_SERVER_ID: 1
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "22181:2181"
    networks:
      - local

  kafka:
    image: confluentinc/cp-kafka:latest
    container_name: kafka-subscriptions
    ports:
      - "3025:3025"
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:13025,PLAINTEXT_HOST://0.0.0.0:3025
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:13025,PLAINTEXT_HOST://localhost:3025
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
    networks:
      - local

  # kafka for notifications
  zookeeper-notification:
    image: confluentinc/cp-zookeeper:latest
    container_name: zookeeper-notification
    environment:
      ZOOKEEPER_SERVER_ID: 1
      ZOOKEEPER_CLIENT_PORT: 2182
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2182:2182"
    networks:
      - local

  kafka-notification:
    image: confluentinc/cp-kafka:latest
    container_name: kafka-notification
    ports:
      - "2025:2025"
    depends_on:
      - zookeeper-notification
    environment:
      KAFKA_BROKER_ID: 2
      KAFKA_ZOOKEEPER_CONNECT: zookeeper-notification:2182
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:12025,PLAINTEXT_HOST://0.0.0.0:2025
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka-notification:12025,PLAINTEXT_HOST://localhost:2025
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
    networks:
      - local

  # ssbms services
  auth-service:
    build: ./authService
    container_name: authservice
    restart: always
    depends_on:
      - postgres
      - keycloak
    ports:
      - "1000:2000"
    environment:
      SPRING_DATASOURCE_URL: ****************************************
      SPRING_DATASOURCE_USERNAME: ssbms
      SPRING_DATASOURCE_PASSWORD: ssbms1234
      APP_KEYCLOAK_SERVERURL: http://keycloak:8080
      KEYCLOAK_ISSUER_URI: http://keycloak:8080/realms/saas
      KEYCLOAK_SERVER_URL: http://keycloak:8080
      SERVER_PORT: 2000
    networks:
      - ssbms-network
      - local

  backoffice-service:
    image: ${CI_REGISTRY_IMAGE}/backoffice-service:${CI_COMMIT_SHA}
    container_name: backofficeservice
    restart: always
    depends_on:
      - postgres
    ports:
      - "1001:2001"
    environment:
      SPRING_DATASOURCE_URL: ****************************************
      SPRING_DATASOURCE_USERNAME: ssbms
      SPRING_DATASOURCE_PASSWORD: ssbms1234
      KEYCLOAK_ISSUER_URI: http://keycloak:8080/realms/saas
      APP_KEYCLOAK_REALM: saas
      SERVER_PORT: 2001
    networks:
      - ssbms-network

  notification-service:
    image: ${CI_REGISTRY_IMAGE}/notification-service:${CI_COMMIT_SHA}
    container_name: notificationservice
    restart: always
    depends_on:
      - zookeeper-notification
      - kafka-notification
    ports:
      - "1003:2003"
    environment:
      SPRING_DATASOURCE_URL: ****************************************
      SPRING_DATASOURCE_USERNAME: ssbms
      SPRING_DATASOURCE_PASSWORD: ssbms1234
      # Use internal Kafka address for container-to-container communication
      SPRING_KAFKA_BOOTSTRAP_SERVERS: kafka-notification:12025
      SERVER_PORT: 2003
    networks:
      - ssbms-network
      - local

  subscription-service:
    image: ${CI_REGISTRY_IMAGE}/subscription-service:${CI_COMMIT_SHA}
    container_name: subscriptionservice
    restart: always
    depends_on:
      - postgres
    ports:
      - "1004:2004"
    environment:
      SPRING_DATASOURCE_URL: ****************************************
      SPRING_DATASOURCE_USERNAME: ssbms
      SPRING_DATASOURCE_PASSWORD: ssbms1234
      KEYCLOAK_ISSUER_URI: http://keycloak:8080/realms/saas
      APP_KEYCLOAK_REALM: saas
      SERVER_PORT: 2004
    networks:
      - ssbms-network
      - local

  payment-service:
    image: ${CI_REGISTRY_IMAGE}/payment-service:${CI_COMMIT_SHA}
    container_name: paymentservice
    restart: always
    depends_on:
      - postgres
      - kafka-notification
      - zookeeper-notification
    ports:
      - "1005:2005"
    environment:
      SPRING_DATASOURCE_URL: ****************************************
      SPRING_DATASOURCE_USERNAME: ssbms
      SPRING_DATASOURCE_PASSWORD: ssbms1234
      # Use internal Kafka address for container-to-container communication
      SPRING_KAFKA_BOOTSTRAP_SERVERS: kafka-notification:12025
      KEYCLOAK_ISSUER_URI: http://keycloak:8080/realms/saas
      APP_KEYCLOAK_REALM: saas
      SERVER_PORT: 2005
    networks:
      - ssbms-network
      - local

  scheduler-service:
    image: ${CI_REGISTRY_IMAGE}/scheduler-service:${CI_COMMIT_SHA}
    container_name: schedulerservice
    restart: always
    depends_on:
      - postgres
      - zookeeper
      - kafka
    ports:
      - "1006:2006"
    environment:
      SPRING_DATASOURCE_URL: ****************************************
      SPRING_DATASOURCE_USERNAME: ssbms
      SPRING_DATASOURCE_PASSWORD: ssbms1234
      # Use internal Kafka address for container-to-container communication
      SPRING_KAFKA_BOOTSTRAP_SERVERS: kafka:13025
      SERVER_PORT: 2006
    networks:
      - local
      - ssbms-network

networks:
  local:
  ssbms-network:

volumes:
  pgadmin:
