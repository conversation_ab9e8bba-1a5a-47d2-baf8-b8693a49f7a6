package com.ssbms.paymentService.repository;

import com.ssbms.paymentService.model.entity.Project;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface ProjectRepository extends JpaRepository<Project, UUID> {

    List<Project> findByBusinessClientId(UUID businessClientId);

    Optional<Project> findByIdAndBusinessClientId(UUID projectId, UUID businessClientId);

    @EntityGraph(attributePaths = {"businessClient"})
    @Query("SELECT p FROM Project p WHERE p.id = :projectId")
    Optional<Project> findByIdWithBusinessClient(@Param("projectId") UUID projectId);
}
