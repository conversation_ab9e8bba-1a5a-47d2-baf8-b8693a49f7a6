-- ================================================
-- SSBMS Database Initialization Script
-- ================================================

\echo '=== Starting Database Initialization ==='

-- Create the ssbms user (with error handling and logging)
DO $$
BEGIN
   IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'ssbms') THEN
      CREATE USER ssbms WITH PASSWORD 'ssbms1234';
      RAISE NOTICE 'User ssbms created successfully.';
   ELSE
      RAISE NOTICE 'User ssbms already exists - skipping creation.';
   END IF;
END
$$;

-- Check ssbms_db database (should exist from POSTGRES_DB env var)
DO $$
BEGIN
   IF EXISTS (SELECT FROM pg_database WHERE datname = 'ssbms_db') THEN
      RAISE NOTICE 'Database ssbms_db exists (as expected from POSTGRES_DB).';
   ELSE
      RAISE NOTICE 'WARNING: Database ssbms_db does not exist!';
   END IF;
END
$$;

-- Create keycloak database with error handling
-- We'll use a script-based approach since CREATE DATABASE can't be in functions

\set ON_ERROR_STOP off
\echo 'Attempting to create keycloak database...'

-- Try to create keycloak database
CREATE DATABASE keycloak OWNER ssbms;

\set ON_ERROR_STOP on

-- Check if keycloak database was created or already exists
DO $$
BEGIN
   IF EXISTS (SELECT FROM pg_database WHERE datname = 'keycloak') THEN
      RAISE NOTICE 'Database keycloak is available.';
   ELSE
      RAISE NOTICE 'ERROR: Database keycloak was not created!';
   END IF;
END
$$;

\echo 'Setting up database permissions...'

-- Grant all privileges on both databases to ssbms user
GRANT ALL PRIVILEGES ON DATABASE ssbms_db TO ssbms;
GRANT ALL PRIVILEGES ON DATABASE keycloak TO ssbms;

\echo 'Configuring ssbms_db schema permissions...'

-- Connect to ssbms_db and grant schema privileges
\c ssbms_db
GRANT ALL ON SCHEMA public TO ssbms;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO ssbms;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO ssbms;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO ssbms;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO ssbms;

\echo 'ssbms_db configuration completed successfully!'

\echo 'Configuring keycloak schema permissions...'

-- Connect to keycloak database and grant schema privileges
\c keycloak
GRANT ALL ON SCHEMA public TO ssbms;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO ssbms;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO ssbms;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO ssbms;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO ssbms;

\echo 'keycloak database configuration completed successfully!'

\echo '=== Database Initialization Completed Successfully! ==='