package com.ssbms.subscriptionService.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ssbms.subscriptionService.client.PaymentServiceRestClient;
import com.ssbms.subscriptionService.exception.InfoRequirement;
import com.ssbms.subscriptionService.exception.UnauthorizedException;
import com.ssbms.subscriptionService.exception.UserNotFoundException;
import com.ssbms.subscriptionService.model.dto.endUserSubsDTO.EndUserSubscriptionRequest;
import com.ssbms.subscriptionService.model.dto.redirect.RedirectRequest;
import com.ssbms.subscriptionService.model.dto.redirect.RedirectResponse;
import com.ssbms.subscriptionService.model.entity.*;
import com.ssbms.subscriptionService.repository.*;
import com.ssbms.subscriptionService.service.EndUserSubscriptionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class EndUserSubscriptionServiceImpl implements EndUserSubscriptionService {

    private final BusinessClientRepository businessClientRepository;
    private final ProjectRepository projectRepository;
    private final ApiKeyRepository apiKeyRepository;
    private final ProjectSettingRepository projectSettingRepository;

    private final PaymentServiceRestClient paymentServiceRestClient;
    private final SubscriptionTempRepository subscriptionTempRepository;

    @Override
    @Transactional
    public RedirectResponse redirectUrl(String apiKey, EndUserSubscriptionRequest request) {

        // 1. Extract business client ID from API key
        UUID businessClientId = extractBusinessClientIdFromApiKey(apiKey);

        // 2. Validate business client exists
        BusinessClient businessClient = validateBusinessClient(businessClientId);

        // 3. Validate request data
        validateRequestData(request);

        // 4. Generate unique reference
        String reference = generateUniqueReference();

        RedirectResponse redirectResponse = new RedirectResponse();

        try {
            // 5. Save to temporary table
            saveToTempTable(reference, businessClientId, request);

            // 6. Get merchant ID
            Project project = getProjectForBusinessClient(businessClient);
            String merchantId = fetchMerchantId(project);

            // 7. Prepare and process payment
            RedirectRequest redirectRequest = prepareTempPaymentRequest(request, reference, merchantId, project);

            // Process payment
            redirectResponse = paymentServiceRestClient.processPayment(redirectRequest);

            // 8. Update temp record status based on payment response
            updateTempRecordStatus(reference, redirectResponse);

            return redirectResponse;
        } catch (Exception e) {
            System.out.println(e.getMessage());
            log.error("Redirecting url failed");
        }
        return redirectResponse;
    }

    private BusinessClient validateBusinessClient(UUID businessClientId) {
        return businessClientRepository.findById(businessClientId)
                .orElseThrow(() -> new UserNotFoundException("Business Client not found"));
    }

    private void validateRequestData(EndUserSubscriptionRequest request) {
        if (request.getUserData() == null) {
            throw new InfoRequirement("User data is required");
        }
        if (request.getSubscriptionPlan() == null) {
            throw new InfoRequirement("Subscription plan is required");
        }
        if (request.getRedirectUrls() == null) {
            throw new InfoRequirement("meta data is required");
        }
    }

    private String generateUniqueReference() {
        return "REF_" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }

    private void saveToTempTable(String reference, UUID businessClientId, EndUserSubscriptionRequest request) {
        SubscriptionTemp temp = new SubscriptionTemp();
        temp.setReference(reference);
        temp.setBusinessClientId(businessClientId);

        // Set Project ID
        Project project = getProjectForBusinessClient(validateBusinessClient(businessClientId));
        temp.setProjectId(project.getId());

        // User Data
        if (request.getUserData() != null) {
            temp.setUserExternalId(request.getUserData().getExternalId());
            temp.setUserEmail(request.getUserData().getEmail());
            temp.setUserUsername(request.getUserData().getUsername());
            temp.setUserAuthProvider(request.getUserData().getAuthProvider());
        }

        // Subscription Plan
        if (request.getSubscriptionPlan() != null) {
            temp.setSubscriptionExternalId(request.getSubscriptionPlan().getExternalId());
            temp.setSystemSubscriptionPlan(request.getSubscriptionPlan().getSystemSubscriptionPlan());
            temp.setSubscriptionStartDate(LocalDateTime.now()); // Current time as start date
            temp.setSubscriptionEndDate(request.getSubscriptionPlan().getEndDate());
            temp.setSubscriptionPrice(request.getSubscriptionPlan().getPrice());
            temp.setBillingInterval(request.getSubscriptionPlan().getBillingInterval());
        }

        // Payment Method
        if (request.getPaymentMethod() != null) {
            temp.setPaymentToken(request.getPaymentMethod().getToken());
            temp.setPaymentCurrency(request.getPaymentMethod().getCurrency());
        }

        // Store redirect URLs
        if (request.getRedirectUrls() != null) {
            temp.setSuccessUrl(request.getRedirectUrls().getSuccessUrl());
            temp.setFailureUrl(request.getRedirectUrls().getFailureUrl());
            temp.setBaseUrl(request.getRedirectUrls().getBaseUrl());
        }

        temp.setCreatedAt(LocalDateTime.now());
        temp.setStatus("PENDING");

        subscriptionTempRepository.save(temp);
    }

    private Project getProjectForBusinessClient(BusinessClient businessClient) {
        return projectRepository.findByBusinessClientId(businessClient.getId()).get(0);
    }

    private RedirectRequest prepareTempPaymentRequest(EndUserSubscriptionRequest request, String reference,
            String merchantId, Project project) {
        // Fetch payment gateway settings from DB
        Map<String, Object> paymentGatewaySettings = fetchPaymentGatewaySettings(project);

        RedirectRequest paymentRequest = new RedirectRequest();
        paymentRequest.setMerchantid(merchantId);
        paymentRequest.setAmount(request.getSubscriptionPlan().getPrice());
        paymentRequest.setCurrency("MAD");
        paymentRequest.setItemid(reference);
        paymentRequest.setIdClient(request.getUserData().getUsername());

        // Set values from payment gateway settings
        paymentRequest.setMerchantname((String) paymentGatewaySettings.get("merchantName"));
        paymentRequest.setWebsitename((String) paymentGatewaySettings.get("websiteName"));
        paymentRequest.setWebsiteid("");
        String callbackUrl = (String) paymentGatewaySettings.get("callbackurl");
        paymentRequest.setCallbackurl(callbackUrl);

        paymentRequest.setLname("");
        paymentRequest.setCountry("");
        paymentRequest.setCity("");
        paymentRequest.setState("");
        paymentRequest.setZipcode("");
        paymentRequest.setAddress("");
        paymentRequest.setPhone("");
        paymentRequest.setToken("");

        if (request.getUserData() != null) {
            paymentRequest.setEmail(request.getUserData().getEmail());
            paymentRequest.setFname(request.getUserData().getUsername());
        }

        return paymentRequest;
    }

    private void updateTempRecordStatus(String reference, RedirectResponse redirectResponse) {
        subscriptionTempRepository.findById(reference).ifPresent(temp -> {
            if (redirectResponse.getStatuscode() == 201) {
                temp.setStatus("PAYMENT_INITIATED");
            } else {
                temp.setStatus("PAYMENT_FAILED");
            }
            subscriptionTempRepository.save(temp);
        });
    }

    public UUID extractBusinessClientIdFromApiKey(String apiKey) {
        // 1. Find the API key entity
        ApiKey apiKeyEntity = apiKeyRepository.findByPublicApiKey(apiKey)
                .orElseThrow(() -> new UnauthorizedException("Invalid API key"));

        // 2. Validate API key status
        if (!apiKeyEntity.isValid()) {
            if (apiKeyEntity.isExpired()) {
                throw new UnauthorizedException("API key has expired");
            }
            if (apiKeyEntity.isRevoked()) {
                throw new UnauthorizedException("API key has been revoked");
            }
            throw new UnauthorizedException("API key is not active");
        }

        // 3. Get the project from API key
        Project project = apiKeyEntity.getProject();
        if (project == null) {
            throw new UnauthorizedException("API key is not associated with any project");
        }

        // 4. Get the business client from project
        BusinessClient businessClient = project.getBusinessClient();
        if (businessClient == null) {
            throw new UnauthorizedException("Project is not associated with any business client");
        }

        // 5. Update API key usage stats
        updateApiKeyUsage(apiKeyEntity);

        return businessClient.getId();
    }

    private void updateApiKeyUsage(ApiKey apiKey) {
        try {
            apiKey.setLastUsedAt(Instant.now());
            apiKey.setUsageCount(apiKey.getUsageCount() + 1);
            apiKeyRepository.save(apiKey);
        } catch (Exception e) {
            log.warn("Failed to update API key usage statistics", e);
        }
    }

    private String fetchMerchantId(Project project) {
        try {
            Map<String, Object> paymentGatewaySettings = fetchPaymentGatewaySettings(project);
            return (String) paymentGatewaySettings.get("merchantId");
        } catch (Exception e) {
            // Fallback to old method if needed
            ProjectSetting merchantIdSetting = projectSettingRepository
                    .findByProjectIdAndKey(project.getId(), "merchantId")
                    .orElseThrow(() -> new RuntimeException("Merchant ID not found in project settings"));
            return merchantIdSetting.getValue();
        }
    }

    private Map<String, Object> fetchPaymentGatewaySettings(Project project) {
        ProjectSetting paymentGatewaySettings = projectSettingRepository
                .findByProjectIdAndKey(project.getId(), "paymentGatewaysSettings")
                .orElseThrow(() -> new RuntimeException("Payment gateway settings not found in project settings"));

        try {
            // Convert the JSON string from DB to a Map
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(paymentGatewaySettings.getValue(), new TypeReference<Map<String, Object>>() {
            });
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to parse payment gateway settings", e);
        }
    }
}