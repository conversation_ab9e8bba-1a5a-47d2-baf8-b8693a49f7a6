package com.ssbms.authService.service;

import com.ssbms.authService.model.dto.LogoutResponse;
import com.ssbms.authService.model.dto.LoginRequest;
import com.ssbms.authService.model.dto.LoginResponse;
import com.ssbms.authService.model.dto.RegisterRequest;
import com.ssbms.authService.model.dto.RegisterResponse;
import org.keycloak.admin.client.resource.UserResource;

import java.util.UUID;

public interface BusinessClientService {

    LoginResponse loginBusinessClient(LoginRequest loginRequest);

    RegisterResponse registerBusinessClient(RegisterRequest registerRequest);

    LogoutResponse logoutBusinessClient(String authHeader, String refreshToken);

    void sendVerificationEmail(String userId);

    // UUID getBusinessClientIdByKeycloakUserId(String name);

    // UserResource getUser(String userId);
}