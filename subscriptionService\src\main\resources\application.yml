# Application configuration
spring:
  application:
    name: subscription-service

  # PostgreSQL database configuration
  datasource:
    url: ${SPRING_DATASOURCE_URL:*****************************************}
    username: ssbms
    password: ssbms1234
    driver-class-name: org.postgresql.Driver

  # JPA (Hibernate) configuration
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    properties:
      hibernate:
        allow-circular-references: true

  jackson:
    serialization:
      FAIL_ON_EMPTY_BEANS: false

server:
  port: ${SERVER_PORT:1004}

# Swagger (OpenAPI) configuration
springdoc:
  swagger-ui:
    path: /swagger-ui.html
  api-docs:
    path: /api-docs

# Logging configuration
logging:
  level:
    org:
      springframework:
        security: DEBUG
        web.client.RestTemplate: DEBUG
    org.apache.tomcat.util.net: DEBUG
    org.springframework.boot.web.embedded.tomcat: DEBUG
    org.springframework.web.reactive.function.client: DEBUG

payment:
  service:
    url: http://localhost:1005/payments
    # url: http://paymentservice:8091/payments

