package com.ssbms.authService.model.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Entity
@Getter
@Setter
@Table(name = "payment_methods")
public class PaymentMethod {

    @Id
    @GeneratedValue
    private UUID id;

    @Column(name = "reference", unique = true)
    private String reference;

    // Relationship with End User
    @OneToOne(mappedBy = "paymentMethod", fetch = FetchType.LAZY)
    @JsonIgnore
    private EndUser endUser;

    @OneToMany(mappedBy = "paymentMethod", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<BillingCycle> billingCycles;

    @Column(name = "payment_type")
    private String paymentType;

    @Column(name = "token")
    private String token;

    @Column(name = "cvv")
    private String cvv;

    @Column(name = "card_type")
    private String cardType;

    @Column(name = "expiry_date")
    private String expiryDate;

    @Column(name = "cardholder_name")
    private String cardholderName;

    @Column(name = "currency")
    private String currency;

    @Transient // This field will not be saved in the database
    private String cardNumber;

    @Column(name = "is-default")
    private boolean isDefault;

    @Column(name = "is_valid")
    private boolean isValid;

    @CreationTimestamp
    @JsonIgnore
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @JsonIgnore
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}