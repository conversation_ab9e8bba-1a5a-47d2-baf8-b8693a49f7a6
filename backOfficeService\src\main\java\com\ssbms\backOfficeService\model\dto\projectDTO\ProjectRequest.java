package com.ssbms.backOfficeService.model.dto.projectDTO;

import com.ssbms.backOfficeService.model.entity.BusinessClient;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectRequest {
    private UUID id;
    private BusinessClient businessClient;

    @NotBlank(message = "Project name is required")
    private String name;
    private String description;
    private String environment;
    private String status;
}