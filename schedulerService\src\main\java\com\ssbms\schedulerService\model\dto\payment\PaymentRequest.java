package com.ssbms.schedulerService.model.dto.payment;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaymentRequest {
    private String currency;
    private String itemid;
    private String merchantid;
    private Double amount;
    private String merchantname;
    private String websitename;
    private String websiteid;
    private String callbackurl;
    private String fname;
    private String lname;
    private String email;
    private String country;
    private String city;
    private String state;
    private String zipcode;
    private String address;
    private String phone;
    private String idClient;
    private String token;
}
