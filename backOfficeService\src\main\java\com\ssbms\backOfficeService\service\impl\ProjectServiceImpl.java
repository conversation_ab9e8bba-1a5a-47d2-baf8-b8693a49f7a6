package com.ssbms.backOfficeService.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ssbms.backOfficeService.exception.RequestException;
import com.ssbms.backOfficeService.exception.CreationException;
import com.ssbms.backOfficeService.exception.NotFoundException;
import com.ssbms.backOfficeService.model.dto.ProjectSettings.ManageProjectSettingsResponse;
import com.ssbms.backOfficeService.model.dto.ProjectSettings.ProjectSettingRequest;
import com.ssbms.backOfficeService.model.dto.projectDTO.*;
import com.ssbms.backOfficeService.model.entity.BusinessClient;
import com.ssbms.backOfficeService.model.entity.Project;
import com.ssbms.backOfficeService.model.entity.ProjectSetting;
import com.ssbms.backOfficeService.repository.BusinessClientRepository;
import com.ssbms.backOfficeService.repository.ProjectRepository;
import com.ssbms.backOfficeService.repository.ProjectSettingRepository;
import com.ssbms.backOfficeService.service.ProjectService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ProjectServiceImpl implements ProjectService {

    private final BusinessClientRepository businessClientRepository;
    private final ProjectRepository projectRepository;
    private final ProjectSettingRepository projectSettingRepository;

    private ObjectMapper objectMapper;

    @Override
    public ProjectResponse createProject(ProjectRequest projectDTO) {
        // Get the authenticated user's ID from Spring Security context (Keycloak JWT)
        String keycloakUserId = SecurityContextHolder.getContext().getAuthentication().getName();

        // Fetch the BusinessClient entity linked to the Keycloak user ID
        BusinessClient businessClient = businessClientRepository.findByKeycloakUserId(keycloakUserId)
                .orElseThrow(() -> new NotFoundException(
                        "Business Client not found for Keycloak User ID: " + keycloakUserId));

        // Create a new Project entity
        Project project = new Project();
        project.setBusinessClient(businessClient);
        project.setName(projectDTO.getName());
        project.setDescription(projectDTO.getDescription());
        project.setEnvironment(projectDTO.getEnvironment());
        project.setStatus("active");

        try {
            // Save to database
            projectRepository.save(project);
            log.info("Project created successfully with ID: {}", project.getId());
            return new ProjectResponse("Project created successfully", 201);
        } catch (DataAccessException e) {
            log.error("Database error while creating project | Client: {}", businessClient.getId(), e);
            throw new CreationException("Failed to save project due to database error: " + e);
        } catch (Exception e) {
            log.error("Unexpected error during project creation | Client: {}", businessClient.getId(), e);
            throw new CreationException("Failed to create project: " + e.getMessage());
        }
    }

    @Override
    public List<ProjectRequest> getProjectsByBusinessClientId() {
        // Get authenticated user's ID from JWT
        String keycloakUserId = SecurityContextHolder.getContext().getAuthentication().getName();

        // Fetch the BusinessClient
        BusinessClient businessClient = businessClientRepository.findByKeycloakUserId(keycloakUserId)
                .orElseThrow(() -> new NotFoundException(
                        "Business Client not found for Keycloak User ID: " + keycloakUserId));

        // Fetch projects linked to the business client
        List<Project> projects = projectRepository.findByBusinessClientId(businessClient.getId());

        if (projects.isEmpty()) {
            throw new NotFoundException("No projects, start your business!");
        }

        // Convert Project entities to DTOs (ProjectRequest)
        List<ProjectRequest> projectRequests = projects.stream()
                .map(project -> new ProjectRequest(
                        project.getId(),
                        project.getBusinessClient(),
                        project.getName(),
                        project.getDescription(),
                        project.getEnvironment(),
                        project.getStatus()))
                .collect(Collectors.toList());

        log.info("Retrieved {} projects for business client ID: {}", projectRequests.size(), businessClient.getId());
        return projectRequests;
    }

    @Override
    public String manageProjectSettings(UUID projectId, List<ProjectSettingRequest> settings) {
        validateSettingsRequest(settings);

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new NotFoundException("Project not found with ID: " + projectId));

        for (ProjectSettingRequest settingRequest : settings) {
            ProjectSetting existingSetting = projectSettingRepository
                    .findByProjectIdAndKey(project.getId(), settingRequest.getKey())
                    .orElse(null);

            if (existingSetting != null) {
                existingSetting.setValueAsObject(settingRequest.getValue());
                existingSetting.setUpdatedAt(LocalDateTime.now());
            } else {
                ProjectSetting newSetting = new ProjectSetting();
                newSetting.setProject(project);
                newSetting.setKey(settingRequest.getKey());
                newSetting.setValueAsObject(settingRequest.getValue());
                newSetting.setCreatedAt(LocalDateTime.now());
                newSetting.setUpdatedAt(LocalDateTime.now());
                existingSetting = newSetting;
            }
            projectSettingRepository.save(existingSetting);
        }

        return "Project settings saved successfully";
    }

    @Override
    public List<ManageProjectSettingsResponse> getAllProjectSettings(UUID projectId) {
        if (!projectRepository.existsById(projectId)) {
            throw new NotFoundException("Project not found with ID: " + projectId);
        }

        List<ProjectSetting> settings = projectSettingRepository.findByProjectId(projectId);

        if (settings.isEmpty()) {
            throw new NotFoundException("No settings found for this project, add some!");
        }

        return settings.stream()
                .map(setting -> new ManageProjectSettingsResponse(
                        setting.getKey(),
                        setting.getValueAsObject(), // Return as object
                        setting.getCreatedAt(),
                        setting.getUpdatedAt()
                ))
                .collect(Collectors.toList());
    }

    private void validateSettingsRequest(List<ProjectSettingRequest> settings) {
        if (settings == null || settings.isEmpty()) {
            throw new RequestException("At least one setting is required");
        }
        settings.forEach(setting -> {
            if (setting.getKey() == null || setting.getKey().trim().isEmpty()) {
                throw new RequestException("Setting key cannot be empty");
            }
            if (setting.getValue() == null) {
                throw new RequestException("Setting value cannot be null");
            }
        });
    }

    // ********************** public endpoints *****************************

    @Override
    public List<ProjectRequest> getProjectsByBusinessClientIdPublic(UUID businessClientId) {
        // Fetch the BusinessClient using the provided ID
        BusinessClient businessClient = businessClientRepository.findById(businessClientId)
                .orElseThrow(() -> new NotFoundException(
                        "Business Client not found with ID: " + businessClientId));

        // Fetch projects
        List<Project> projects = projectRepository.findByBusinessClientId(businessClient.getId());

        if (projects.isEmpty()) {
            throw new NotFoundException(
                    "No projects found for Business Client ID: " + businessClient.getId());
        }

        // Convert to DTO and return
        List<ProjectRequest> projectRequests = projects.stream()
                .map(project -> new ProjectRequest(
                        project.getId(),
                        project.getBusinessClient(),
                        project.getName(),
                        project.getDescription(),
                        project.getEnvironment(),
                        project.getStatus()))
                .collect(Collectors.toList());

        // log.info("Retrieved {} projects for business client ID: {}", projectRequests.size(), businessClient.getId());
        return projectRequests;
    }

    @Override
    public ProjectResponse createProjectPublic(UUID businessClientId, ProjectRequest projectDTO) {
        // Validate required fields
        // validateProjectRequest(projectDTO);

        // Fetch the BusinessClient using the provided ID
        BusinessClient businessClient = businessClientRepository.findById(businessClientId)
                .orElseThrow(() -> new NotFoundException(
                        "Business Client not found with ID: " + businessClientId));

        // Create and save Project
        Project project = new Project();
        project.setBusinessClient(businessClient);
        project.setName(projectDTO.getName());
        project.setDescription(projectDTO.getDescription());
        project.setEnvironment("development");
        project.setStatus("active");

        try {
            projectRepository.save(project);
            log.info("Project created successfully with ID: {} for Business Client ID: {}",
                    project.getId(), businessClient.getId());
            return new ProjectResponse("Project created successfully", 201);
        } catch (DataAccessException e) {
            log.error("Database error while creating project | Client: {}", businessClient.getId(), e);
            throw new CreationException("Failed to save project due to database error: " + e);
        } catch (Exception e) {
            log.error("Unexpected error during project creation | Client: {}", businessClient.getId(), e);
            throw new CreationException("Failed to create project: " + e.getMessage());
        }
    }

    @Override
    public void updateProjectEnvironment(UUID projectId, UpdateEnvironmentRequest request) {
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new NotFoundException("Project not found"));

        project.setEnvironment(request.getEnvironment());
        projectRepository.save(project);
    }
}
