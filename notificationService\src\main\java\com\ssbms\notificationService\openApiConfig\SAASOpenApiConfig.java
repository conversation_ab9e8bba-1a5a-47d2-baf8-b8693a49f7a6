package com.ssbms.notificationService.openApiConfig;

import io.swagger.v3.oas.models.ExternalDocumentation;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SAASOpenApiConfig {

    @Bean
    public OpenAPI businessClientAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Notification service")
                        .description("This is the REST API for notification service")
                        .version("v0.0.1")
                        .license(new License().name("Apache 2.0"))
                )
                .externalDocs(new ExternalDocumentation()
                        .description("Here the reference to the notification service documentation")
                        .url("https://business_client.com/docs"));
    }
}
