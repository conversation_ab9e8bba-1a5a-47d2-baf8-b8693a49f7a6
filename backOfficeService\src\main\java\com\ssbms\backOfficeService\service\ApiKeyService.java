package com.ssbms.backOfficeService.service;

import com.ssbms.backOfficeService.model.dto.apiKeyDTO.ApiKeyListResponse;
import com.ssbms.backOfficeService.model.dto.apiKeyDTO.ApiKeyRequest;
import com.ssbms.backOfficeService.model.dto.apiKeyDTO.ApiKeyResponse;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

public interface ApiKeyService {

    ApiKeyResponse createApiKey(UUID projectId, ApiKeyRequest apiKeyRequest);

    List<ApiKeyListResponse> getAllApiKeysForProject(UUID projectId);

    UUID extractProjectId(String apiKey);

    @Transactional
    void revokeApiKey(UUID apiKeyId);

    // ********************** Public API Key Endpoints *****************************

    ApiKeyResponse createApiKeyPublic(UUID projectId, ApiKeyRequest apiKeyRequest);
    List<ApiKeyListResponse> getAllApiKeysForProjectPublic(UUID projectId);
    void revokeApiKeyPublic(UUID projectId, UUID apiKeyId);
}
