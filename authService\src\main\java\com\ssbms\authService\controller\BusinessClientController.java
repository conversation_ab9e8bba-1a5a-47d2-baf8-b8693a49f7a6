package com.ssbms.authService.controller;

import com.ssbms.authService.model.dto.LogoutRequest;
import com.ssbms.authService.model.dto.LogoutResponse;
import com.ssbms.authService.model.dto.LoginRequest;
import com.ssbms.authService.model.dto.LoginResponse;
import com.ssbms.authService.model.dto.RegisterRequest;
import com.ssbms.authService.model.dto.RegisterResponse;
import com.ssbms.authService.service.BusinessClientService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/auth/users")
@RequiredArgsConstructor
public class BusinessClientController {

    private final BusinessClientService businessClientService;

    @PostMapping("/login")
    public ResponseEntity<LoginResponse> login(@Valid @RequestBody LoginRequest loginRequest) {
        LoginResponse response = businessClientService.loginBusinessClient(loginRequest);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/register")
    public ResponseEntity<RegisterResponse> register(@Valid @RequestBody RegisterRequest registerRequest) {
        RegisterResponse response = businessClientService.registerBusinessClient(registerRequest);
        return ResponseEntity.status(response.getStatus()).body(response);
    }

    @PutMapping("/{id}/send-verification-email")
    public ResponseEntity<Void> sendVerificationEmail(@PathVariable String id) {
        businessClientService.sendVerificationEmail(id);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/logout")
    public ResponseEntity<LogoutResponse> logout(
            @RequestHeader("Authorization") String authHeader,
            @RequestBody LogoutRequest logoutRequest) {
        LogoutResponse response = businessClientService.logoutBusinessClient(authHeader, logoutRequest.getRefreshToken());
        return ResponseEntity.ok(response);
    }
}
