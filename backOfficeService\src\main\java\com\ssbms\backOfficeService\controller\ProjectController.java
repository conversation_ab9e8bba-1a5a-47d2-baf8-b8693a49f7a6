package com.ssbms.backOfficeService.controller;

import com.ssbms.backOfficeService.model.dto.ProjectSettings.ManageProjectSettingsRequest;
import com.ssbms.backOfficeService.model.dto.ProjectSettings.ManageProjectSettingsResponse;
import com.ssbms.backOfficeService.model.dto.projectDTO.UpdateEnvironmentRequest;
import com.ssbms.backOfficeService.model.dto.projectDTO.*;
import com.ssbms.backOfficeService.service.ProjectService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/projects")
@RequiredArgsConstructor
public class ProjectController {

    private final ProjectService projectService;

    @PostMapping
    public ResponseEntity<ProjectResponse> createNewProject(@Valid @RequestBody ProjectRequest projectDTO) {
        ProjectResponse createdProject = projectService.createProject(projectDTO);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdProject);
    }

    @GetMapping
    public ResponseEntity<List<ProjectRequest>> getProjects() {
        List<ProjectRequest> projects = projectService.getProjectsByBusinessClientId();
        return ResponseEntity.status(HttpStatus.OK).body(projects);
    }

    @PostMapping("/settings/manage")
    public ResponseEntity<String> manageProjectSettings(@Valid @RequestBody ManageProjectSettingsRequest request) {
        String result = projectService.manageProjectSettings(request.getProjectId(), request.getSettings());
        return ResponseEntity.status(HttpStatus.OK).body(result);
    }

    @GetMapping("/settings/{projectId}")
    public ResponseEntity<List<ManageProjectSettingsResponse>> getAllProjectSettings(@PathVariable("projectId") UUID projectId) {
        List<ManageProjectSettingsResponse> settings = projectService.getAllProjectSettings(projectId);
        return ResponseEntity.ok(settings);
    }

    @PatchMapping("/{projectId}/environment")
    public ResponseEntity<String> updateProjectEnvironment(@PathVariable UUID projectId, @RequestBody UpdateEnvironmentRequest request) {
        projectService.updateProjectEnvironment(projectId, request);
        return ResponseEntity.ok("Environment updated successfully");
    }

    // ********************** public endpoints *****************************
    @GetMapping("/public/{businessClientId}")
    public ResponseEntity<List<ProjectRequest>> getProjectsByBusinessClientId(@PathVariable UUID businessClientId) {
        List<ProjectRequest> projects = projectService.getProjectsByBusinessClientIdPublic(businessClientId);
        return ResponseEntity.status(HttpStatus.OK).body(projects);
    }

    @PostMapping("/public/{businessClientId}")
    public ResponseEntity<ProjectResponse> createProjectPublic(@PathVariable UUID businessClientId, @RequestBody ProjectRequest projectDTO) {
        ProjectResponse createdProject = projectService.createProjectPublic(businessClientId, projectDTO);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdProject);
    }

    @PostMapping("/public/settings/manage")
    public ResponseEntity<String> manageProjectSettingsPublic(@RequestBody ManageProjectSettingsRequest request) {
        String result = projectService.manageProjectSettings(request.getProjectId(), request.getSettings());
        return ResponseEntity.status(HttpStatus.OK).body(result);
    }

    @GetMapping("/public/settings/{projectId}")
    public ResponseEntity<List<ManageProjectSettingsResponse>> getAllProjectSettingsPublic(@PathVariable("projectId") UUID projectId) {
        List<ManageProjectSettingsResponse> settings = projectService.getAllProjectSettings(projectId);
        return ResponseEntity.ok(settings);
    }

    @PatchMapping("/public/{projectId}/environment")
    public ResponseEntity<String> updateProjectEnvironmentPublic(@PathVariable UUID projectId, @RequestBody UpdateEnvironmentRequest request) {
        projectService.updateProjectEnvironment(projectId, request);
        return ResponseEntity.ok("Environment updated successfully");
    }
}