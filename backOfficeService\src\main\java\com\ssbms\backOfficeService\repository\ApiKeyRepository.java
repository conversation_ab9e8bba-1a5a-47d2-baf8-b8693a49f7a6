package com.ssbms.backOfficeService.repository;

import com.ssbms.backOfficeService.model.entity.ApiKey;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface ApiKeyRepository extends JpaRepository<ApiKey, UUID> {

    @Query("SELECT ak FROM ApiKey ak JOIN FETCH ak.project p JOIN FETCH p.businessClient WHERE ak.id = :id")
    Optional<ApiKey> findByIdWithProject(@Param("id") UUID id);

    List<ApiKey> findByKeyPrefix(String prefix);

    List<ApiKey> findByIsActiveTrue();

    List<ApiKey> findByProjectId(UUID projectId);

    Optional<ApiKey> findByIdAndProjectId(UUID id, UUID projectId);

    List<ApiKey> findByProjectIdAndIsActive(UUID projectId, boolean isActive);
}