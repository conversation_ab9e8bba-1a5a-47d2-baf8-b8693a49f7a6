package com.ssbms.paymentService.service.impl;

import com.ssbms.paymentService.exception.NotFoundException;
import com.ssbms.paymentService.exception.RequestException;
import com.ssbms.paymentService.exception.PaymentProcessingException;
import com.ssbms.paymentService.kafka.NotificationProducer;
import com.ssbms.paymentService.model.dto.payment.RedirectRequest;
import com.ssbms.paymentService.model.dto.payment.RedirectResponse;
import com.ssbms.paymentService.model.dto.payment.WalletApiRequest;
import com.ssbms.paymentService.model.dto.webhook.WebhookData;
import com.ssbms.paymentService.model.entity.*;
import com.ssbms.paymentService.model.enums.BillingInterval;
import com.ssbms.paymentService.repository.*;
import com.ssbms.paymentService.service.InvoiceService;
import com.ssbms.paymentService.service.PaymentService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Service
@RequiredArgsConstructor
@Slf4j
public class PaymentServiceImpl implements PaymentService {

    private final ProjectRepository projectRepository;
    private final SubscriptionTempRepository subscriptionTempRepository;
    private final EndUserRepository endUserRepository;
    private final EndUserSubscriptionRepository endUserSubscriptionRepository;
    private final PaymentMethodRepository paymentMethodRepository;
    private final PaymentTransactionRepository paymentTransactionRepository;
    private final FailedPaymentRepository failedPaymentRepository;
    private final BillingCycleRepository billingCycleRepository;
    private final InvoiceService invoiceService;

    private final RestTemplate restTemplate;
    private final NotificationProducer notificationProducer;

    @Value("${payment.wallet.api.url}")
    private String urlToPayGat;

    @Override
    @Transactional
    public RedirectResponse redirectToCheckout(RedirectRequest paymentRequest) {

        try {
            validateRedirectRequest(paymentRequest);

            log.debug("Final request to payment gateway: {}", paymentRequest);

            // Call the external wallet API
            RedirectResponse redirectResponse = callWalletApiForCheckoutUrl(paymentRequest);

            if (redirectResponse.getUrl() != null) {
                System.out.println("redirected url : " + redirectResponse.getUrl());

                return redirectResponse;
            } else {
                throw new PaymentProcessingException("Redirecting failed with status: " + redirectResponse.getStatus());
            }
        } catch (Exception e) {
            log.error("⚠ redirecting failed: {}", e.getMessage(), e);
            throw new PaymentProcessingException("redirecting failed: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public WebhookData handlePaymentScheduler(RedirectRequest paymentRequest) {
        try {
            validateRedirectRequest(paymentRequest);

            // 1. Call the external wallet API
            WebhookData webhookData = callWalletApiForScheduler(paymentRequest);

            // 2. Extract reference from request
            String reference = paymentRequest.getItemid();

            // 3. Find the temporary record
            SubscriptionTemp tempRecord = subscriptionTempRepository.findByReference(reference)
                    .orElseThrow(() -> {
                        log.error("No temporary record found for reference: {}", reference);
                        return new NotFoundException("Subscription not found for reference: " + reference);
                    });

            // 4. Process based on payment status
            if ("00".equals(webhookData.getRepauto())) {
                log.info("Processing successful scheduler payment for reference: {}", reference);
                handleSuccessfulSchedulerPayment(tempRecord, webhookData);
            } else {
                log.warn("Processing failed scheduler payment for reference: {}", reference);
                handleFailedPayment(tempRecord, webhookData);
            }

            return webhookData;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional
    public void handlePaymentWebhook(WebhookData webhookData, HttpServletResponse response) throws IOException {
        String reference = webhookData.getIdcommande();

        String targetUrl = null;
        try {
            // Get the temporary record with URLs
            SubscriptionTemp tempRecord = getSubscriptionTemp(reference);

            // Determine which URL to use based on payment status
            targetUrl = "00".equals(webhookData.getRepauto())
                    ? tempRecord.getSuccessUrl()
                    : tempRecord.getFailureUrl();

            // Process the payment
            if ("00".equals(webhookData.getRepauto())) {
                handleSuccessfulPayment(tempRecord, webhookData);
            } else {
                handleFailedPayment(tempRecord, webhookData);
            }

            // Redirect to the appropriate frontend URL
            redirectToFrontend(response, targetUrl, reference, "SUCCESS");

        } catch (NotFoundException e) {
            // Fallback to default failure URL if record not found
            log.error("Temporary record not found for reference: {}", reference);
            redirectToFrontend(response, targetUrl, reference, "FAILURE");
        }
    }

    // helper methods
    private void validateRedirectRequest(RedirectRequest paymentRequest) {
        if (paymentRequest == null) {
            throw new RequestException("Payment request cannot be null");
        }
        if (paymentRequest.getAmount() == null || paymentRequest.getAmount() <= 0) {
            throw new RequestException("Invalid payment amount");
        }
    }

    private RedirectResponse callWalletApiForCheckoutUrl(RedirectRequest paymentRequest) {
        WalletApiRequest walletApiRequest = new WalletApiRequest(
                paymentRequest.getCurrency(),
                paymentRequest.getItemid(),
                paymentRequest.getMerchantid(),
                paymentRequest.getAmount(),
                paymentRequest.getMerchantname(),
                paymentRequest.getWebsitename(),
                paymentRequest.getWebsiteid(),
                paymentRequest.getCallbackurl(),
                paymentRequest.getFname(),
                paymentRequest.getLname(),
                paymentRequest.getEmail(),
                paymentRequest.getCountry(),
                paymentRequest.getCity(),
                paymentRequest.getState(),
                paymentRequest.getZipcode(),
                paymentRequest.getAddress(),
                paymentRequest.getPhone(),
                paymentRequest.getId_client(),
                paymentRequest.getToken());

        // Make the API call
        return restTemplate.postForObject(urlToPayGat, walletApiRequest, RedirectResponse.class);
    }

    private WebhookData callWalletApiForScheduler(RedirectRequest paymentRequest) {
        WalletApiRequest walletApiRequest = new WalletApiRequest(
                paymentRequest.getCurrency(),
                paymentRequest.getItemid(),
                paymentRequest.getMerchantid(),
                paymentRequest.getAmount(),
                paymentRequest.getMerchantname(),
                paymentRequest.getWebsitename(),
                paymentRequest.getWebsiteid(),
                paymentRequest.getCallbackurl(),
                paymentRequest.getFname(),
                paymentRequest.getLname(),
                paymentRequest.getEmail(),
                paymentRequest.getCountry(),
                paymentRequest.getCity(),
                paymentRequest.getState(),
                paymentRequest.getZipcode(),
                paymentRequest.getAddress(),
                paymentRequest.getPhone(),
                paymentRequest.getId_client(),
                paymentRequest.getToken());

        // Make the API call and return WebhookData directly
        return restTemplate.postForObject(urlToPayGat, walletApiRequest, WebhookData.class);
    }

    private void handleSuccessfulPayment(SubscriptionTemp tempRecord, WebhookData webhookData) {
        try {
            // 1. Get project
            Project project = projectRepository.findById(tempRecord.getProjectId())
                    .orElseThrow(() -> new NotFoundException("Project not found"));

            // 2. Create and save all entities with the same reference
            EndUser endUser = createEndUser(tempRecord, project);
            PaymentMethod paymentMethod = createPaymentMethod(tempRecord, endUser, webhookData);
            EndUserSubscription subscription = createSubscription(tempRecord, endUser);
            createBillingCycles(subscription, tempRecord, paymentMethod);
            PaymentTransaction transaction = createTransaction(webhookData, paymentMethod);

            // generate invoice
            invoiceService.generateInvoice(transaction);

            // 3. Update temp record status
            tempRecord.setStatus("COMPLETED");
            subscriptionTempRepository.save(tempRecord);

            // 4. Send notification with transaction ID
            notificationProducer.sendWelcomeEmail(transaction.getId());

            log.info("Successfully processed payment for reference: {}", tempRecord.getReference());

        } catch (Exception e) {
            log.error("Failed to process successful payment for reference: {}", tempRecord.getReference(), e);
            throw new PaymentProcessingException("Failed to process successful payment");
        }
    }

    private void handleSuccessfulSchedulerPayment(SubscriptionTemp tempRecord, WebhookData webhookData) {
        try {
            // 1. First create and persist all entities
            EndUserSubscription subscription = endUserSubscriptionRepository
                    .findByReference(webhookData.getIdcommande())
                    .orElseThrow(() -> new NotFoundException(
                            "Subscription with reference " + webhookData.getIdcommande() + " not found"));
            PaymentMethod paymentMethod = paymentMethodRepository.findByReference(webhookData.getIdcommande())
                    .orElseThrow(() -> new NotFoundException(
                            "payment method with reference " + webhookData.getIdcommande() + " not found"));
            createBillingCyclesScheduler(subscription, tempRecord, paymentMethod);
            PaymentTransaction transaction = createTransaction(webhookData, paymentMethod);

            // 3. Update temp record status
            tempRecord.setStatus("COMPLETED");
            subscriptionTempRepository.save(tempRecord);

            paymentTransactionRepository.flush();
            // 4. Send notification with transaction ID
            notificationProducer.sendPaymentConfirmation(transaction.getId());
        } catch (Exception e) {
            log.error("Failed to process successful payment for reference: {}", tempRecord.getReference(), e);
            throw new PaymentProcessingException("Failed to process successful payment");
        }
    }

    private void handleFailedPayment(SubscriptionTemp tempRecord, WebhookData webhookData) {
        try {
            FailedPayment failedPayment = new FailedPayment();
            failedPayment.setReference(tempRecord.getReference());
            failedPayment.setAmount(webhookData.getMontant());
            failedPayment.setCurrency("MAD");
            failedPayment.setFailureDate(LocalDateTime.now());
            failedPayment.setFailureReason(webhookData.getRepauto());
            failedPayment.setGatewayErrorMessage("Payment failed with code: " + webhookData.getRepauto());
            failedPaymentRepository.save(failedPayment);

            // Update temp record status
            tempRecord.setStatus("FAILED");
            subscriptionTempRepository.save(tempRecord);

            log.warn("Processed failed payment for reference: {}", tempRecord.getReference());
        } catch (Exception e) {
            log.error("Failed to process failed payment for reference: {}", tempRecord.getReference(), e);
            throw new PaymentProcessingException("Failed to process failed payment");
        }
    }

    private EndUser createEndUser(SubscriptionTemp tempRecord, Project project) {
        EndUser endUser = new EndUser();
        endUser.setReference(tempRecord.getReference());
        endUser.setProject(project);
        endUser.setExternalId(tempRecord.getUserExternalId());
        endUser.setEmail(tempRecord.getUserEmail());
        endUser.setUsername(tempRecord.getUserUsername());
        endUser.setAuthProvider(tempRecord.getUserAuthProvider());
        endUser.setStatus("active");
        return endUserRepository.save(endUser);
    }

    private PaymentMethod createPaymentMethod(SubscriptionTemp tempRecord, EndUser endUser, WebhookData webhookRequest) {
        PaymentMethod paymentMethod = new PaymentMethod();
        paymentMethod.setReference(tempRecord.getReference());
        paymentMethod.setEndUser(endUser);
        paymentMethod.setToken(tempRecord.getPaymentToken());
        paymentMethod.setCurrency(tempRecord.getPaymentCurrency());
        paymentMethod.setToken(webhookRequest.getToken());

        paymentMethod.setEndUser(endUser);
        endUser.setPaymentMethod(paymentMethod);
        return paymentMethodRepository.save(paymentMethod);
    }

    private EndUserSubscription createSubscription(SubscriptionTemp tempRecord, EndUser endUser) {
        EndUserSubscription subscription = new EndUserSubscription();
        subscription.setReference(tempRecord.getReference());
        subscription.setEndUser(endUser);
        subscription.setSystemSubscriptionPlan(tempRecord.getSystemSubscriptionPlan());
        subscription.setStartDate(LocalDateTime.now());
        subscription.setEndDate(tempRecord.getSubscriptionEndDate());
        subscription.setPrice(tempRecord.getSubscriptionPrice());
        subscription.setBillingInterval(tempRecord.getBillingInterval());
        subscription.setStatus("ACTIVE");
        return endUserSubscriptionRepository.save(subscription);
    }

    private PaymentTransaction createTransaction(WebhookData webhookData, PaymentMethod paymentMethod) {
        PaymentTransaction transaction = new PaymentTransaction();
        transaction.setReference(paymentMethod.getReference()); // Same reference as parent entities
        transaction.setPaymentMethod(paymentMethod);
        transaction.setAmount(webhookData.getMontant());
        transaction.setCurrency("MAD");
        transaction.setPaymentProcessor("external provider");
        transaction.setStatus("COMPLETED");
        transaction.setTransactionReference(webhookData.getNumTrans());

        if (webhookData.getCarte() != null && webhookData.getCarte().length() >= 4) {
            transaction.setCardLastFour(webhookData.getCarte().substring(webhookData.getCarte().length() - 4));
        }

        transaction.setProcessedAt(LocalDateTime.now());
        // Only send notification after everything is committed
        return paymentTransactionRepository.saveAndFlush(transaction);
    }

    @Transactional
    private void createBillingCycles(EndUserSubscription subscription, SubscriptionTemp tempRecord, PaymentMethod paymentMethod) {
        // Create and save the first billing cycle with status = "PAID"
        BillingCycle firstBillingCycle = new BillingCycle();
        firstBillingCycle.setSubscription(subscription);
        firstBillingCycle.setBillingDate(LocalDateTime.now());
        firstBillingCycle.setBaseAmount(tempRecord.getSubscriptionPrice());
        firstBillingCycle.setTotalAmount(tempRecord.getSubscriptionPrice());
        firstBillingCycle.setCurrencyCode("MAD");
        firstBillingCycle.setStatus("PAID");
        firstBillingCycle.setPaymentMethod(paymentMethod);
        firstBillingCycle.setDescription("Initial billing cycle for subscription " + subscription.getId());
        billingCycleRepository.save(firstBillingCycle);

        // Create and save the next billing cycle with status = "PENDING"
        LocalDateTime nextBillingDate = calculateNextBillingDate(
                LocalDateTime.now(),
                subscription.getBillingInterval());

        BillingCycle nextBillingCycle = new BillingCycle();
        nextBillingCycle.setSubscription(subscription);
        nextBillingCycle.setBillingDate(nextBillingDate);
        nextBillingCycle.setBaseAmount(tempRecord.getSubscriptionPrice());
        nextBillingCycle.setTotalAmount(tempRecord.getSubscriptionPrice());
        nextBillingCycle.setCurrencyCode("MAD");
        nextBillingCycle.setStatus("PENDING");
        nextBillingCycle.setPaymentMethod(paymentMethod);
        nextBillingCycle.setDescription("Next billing cycle for subscription " + subscription.getId());
        billingCycleRepository.save(nextBillingCycle);
    }

    @Transactional
    private void createBillingCyclesScheduler(EndUserSubscription subscription, SubscriptionTemp tempRecord, PaymentMethod paymentMethod) {
        // get the billing cycle for this subscription
        BillingCycle billingCycle = billingCycleRepository
                .findBySubscriptionIdAndStatus(subscription.getId(), "PENDING")
                .orElseThrow(() -> new NotFoundException(
                        "Pending billing cycle for subscription " + subscription.getId() + " not found"));

        billingCycle.setStatus("PAID");
        billingCycleRepository.save(billingCycle);

        // Create and save the next billing cycle with status = "PENDING"
        LocalDateTime nextBillingDate = calculateNextBillingDate(
                LocalDateTime.now(),
                subscription.getBillingInterval());

        BillingCycle nextBillingCycle = new BillingCycle();
        nextBillingCycle.setSubscription(subscription);
        nextBillingCycle.setBillingDate(nextBillingDate);
        nextBillingCycle.setBaseAmount(tempRecord.getSubscriptionPrice());
        nextBillingCycle.setTotalAmount(tempRecord.getSubscriptionPrice());
        nextBillingCycle.setCurrencyCode("MAD");
        nextBillingCycle.setStatus("PENDING");
        nextBillingCycle.setPaymentMethod(paymentMethod);
        nextBillingCycle.setDescription("Next billing cycle for subscription " + subscription.getId());
        billingCycleRepository.save(nextBillingCycle);
    }

    private LocalDateTime calculateNextBillingDate(LocalDateTime currentBillingDate, BillingInterval billingInterval) {
        return switch (billingInterval) {
            case TWOMINUTE -> currentBillingDate.plusMinutes(2);
            case HALFHOUR -> currentBillingDate.plusMinutes(30);
            case HOUR -> currentBillingDate.plusHours(1);
            case FIVEHOUR -> currentBillingDate.plusHours(5);
            case DAILY -> currentBillingDate.plusDays(1);
            case MONTHLY -> currentBillingDate.plusMonths(1);
            case YEARLY -> currentBillingDate.plusYears(1);
            default -> throw new IllegalArgumentException("Invalid billing interval: " + billingInterval);
        };
    }

    private SubscriptionTemp getSubscriptionTemp(String reference) {
        return subscriptionTempRepository.findByReference(reference)
                .orElseThrow(() -> new NotFoundException("Subscription not found for reference: " + reference));
    }

    private void redirectToFrontend(HttpServletResponse response, String baseUrl, String reference, String status) throws IOException {
        // Build the complete redirect URL with query parameters
        String redirectUrl = UriComponentsBuilder.fromUriString(baseUrl)
                .queryParam("reference", reference)
                .queryParam("status", status)
                .build()
                .toUriString();

        log.info("Redirecting to frontend: {}", redirectUrl);
        response.sendRedirect(redirectUrl);
    }
}