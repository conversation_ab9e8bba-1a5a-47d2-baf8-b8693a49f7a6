package com.ssbms.paymentService.service;

import com.ssbms.paymentService.model.dto.invoice.InvoiceResponse;
import com.ssbms.paymentService.model.entity.PaymentTransaction;
import org.springframework.core.io.Resource;

import java.util.List;
import java.util.UUID;

public interface InvoiceService {

    void generateInvoice(PaymentTransaction transaction);

    List<InvoiceResponse> getInvoicesByEmail(String email);

    Resource downloadInvoice(UUID invoiceId);

    Resource downloadInvoiceByNumber(String invoiceNumber);
}
