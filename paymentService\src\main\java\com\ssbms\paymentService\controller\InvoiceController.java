package com.ssbms.paymentService.controller;

import com.ssbms.paymentService.model.dto.invoice.InvoiceResponse;
import com.ssbms.paymentService.service.InvoiceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/invoices")
@RequiredArgsConstructor
@Slf4j
public class InvoiceController {

    private final InvoiceService invoiceService;

    @GetMapping("/by-email/{email}")
    public ResponseEntity<List<InvoiceResponse>> getInvoicesByEmail(@PathVariable String email) {
        log.info("Received request to get invoices for user with email: {}", email);
        List<InvoiceResponse> invoices = invoiceService.getInvoicesByEmail(email);
        return ResponseEntity.ok(invoices);
    }

    @GetMapping("/download/{invoiceId}")
    public ResponseEntity<Resource> downloadInvoice(@PathVariable UUID invoiceId) {
        log.info("Received request to download invoice with ID: {}", invoiceId);
        Resource resource = invoiceService.downloadInvoice(invoiceId);

        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_PDF)
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"invoice.pdf\"")
                .body(resource);
    }

    @GetMapping("/download/by-number/{invoiceNumber}")
    public ResponseEntity<Resource> downloadInvoiceByNumber(@PathVariable String invoiceNumber) {
        log.info("Received request to download invoice with number: {}", invoiceNumber);
        Resource resource = invoiceService.downloadInvoiceByNumber(invoiceNumber);

        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_PDF)
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"invoice.pdf\"")
                .body(resource);
    }
}
