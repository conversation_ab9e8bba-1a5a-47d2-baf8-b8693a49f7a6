package com.ssbms.paymentService.model.dto.endUserSubsDTO;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ssbms.paymentService.model.enums.BillingInterval;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class EndUserSubscriptionRequest {

    @JsonProperty("user_data") // Map JSON field "user_data" to Java field "userData"
    private UserData userData;

    @JsonProperty("subscription_plan") // Map JSON field "subscription_plan" to Java field "subscriptionPlan"
    private SubscriptionPlan subscriptionPlan;

    @JsonProperty("payment_method") // Map JSON field "payment_method" to Java field "paymentMethod"
    private PaymentMethodData paymentMethod;

    @Data
    public static class UserData {
        private String externalId;
        private String email;
        private String username;
        private String authProvider;
    }

    @Data
    public static class SubscriptionPlan {
        private String externalId;
        private String systemSubscriptionPlan;
        private LocalDateTime startDate;
        private LocalDateTime endDate;
        private Double price;
        private BillingInterval billingInterval;
    }

    @Data
    public static class PaymentMethodData {
        private String token;
        private String currency;
    }
}