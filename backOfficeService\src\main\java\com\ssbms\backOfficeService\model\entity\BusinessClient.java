package com.ssbms.backOfficeService.model.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Entity
@Table(name = "business_clients")
@Getter
@Setter
public class BusinessClient {

    @Id
    @GeneratedValue
    private UUID id;

    // Relationship with Projects
    @OneToMany(mappedBy = "businessClient", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private List<Project> projects;

    @Column(name = "keycloak_user_id", nullable = false, unique = true)
    private String keycloakUserId;

    // Business Information
    @Column(name = "business_name", nullable = true)
    private String businessName;

    @Column(name = "business_type")
    private String businessType;

    @Column(name = "industry")
    private String industry;

    @Column(name = "company_size")
    private String companySize;

    // Contact Information
    @Column(name = "primary_contact_name")
    private String primaryContactName;

    @Column(name = "contact_email", nullable = true)
    private String contactEmail;

    @Column(name = "contact_phone")
    private String contactPhone;

    // Address
    private String addressLine1;
    private String addressLine2;
    private String city;
    private String stateProvince;
    private String postalCode;
    private String country;

    // System Information
    @Column(name = "verification_status", nullable = false)
    private String verificationStatus = "PENDING";

    @Column(name = "onboarding_completed")
    private Boolean onboardingCompleted = false;

    @Column(name = "active")
    private Boolean active = true;

    @CreationTimestamp
    @JsonIgnore
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    @JsonIgnore
    private LocalDateTime updatedAt;

    @Column(name = "verified_at")
    private LocalDateTime verifiedAt;

    // SaaS Settings
    @Column(name = "timezone")
    private String timezone = "UTC";

    @Column(name = "locale")
    private String locale = "en_US";

    // Communication Preferences
    private Boolean marketingEmailsOptIn = false;
    private Boolean invoiceNotificationEmails = true;
    private Boolean paymentReminderEmails = true;
    private Boolean serviceUpdateEmails = true;

    // Notification Settings
    private Boolean emailNotifications = true;
    private Boolean smsNotifications = false;

    // Additional Information
    private String referralSource;

    @Column(columnDefinition = "TEXT")
    private String notes;
}








