package com.ssbms.backOfficeService.model.dto.apiKeyDTO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApiKeyListResponse {
    private UUID id;
    private String keyName;
    private String apiKey;
    private String keyPrefix;
    private boolean active;
    private LocalDateTime createdAt;
    private Instant lastUsedAt;
    private Instant revokedAt;
    private Instant expiresAt;
    private Long usageCount;
}