package com.ssbms.paymentService.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ssbms.paymentService.model.dto.payment.RedirectRequest;
import com.ssbms.paymentService.model.dto.payment.RedirectResponse;
import com.ssbms.paymentService.model.dto.webhook.WebhookData;
import com.ssbms.paymentService.service.PaymentService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

@RestController
@RequestMapping("/payments")
@RequiredArgsConstructor
@Slf4j
public class PaymentController {

    private final PaymentService paymentService;
    private final ObjectMapper objectMapper;

    @PostMapping("/redirectUrl")
    public ResponseEntity<RedirectResponse> processPayment(@Valid @RequestBody RedirectRequest paymentRequest) {

        RedirectResponse response = paymentService.redirectToCheckout(paymentRequest);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/process-scheduled")
    public ResponseEntity<WebhookData> processScheduledPayment(@Valid @RequestBody RedirectRequest paymentRequest) {
        System.out.println("payment request received from scheduler **************" + paymentRequest);

        WebhookData webhookData = paymentService.handlePaymentScheduler(paymentRequest);
        return ResponseEntity.ok(webhookData);
    }

    @GetMapping("/webhook")
    public void handlePaymentWebhook(
            @RequestParam("data") String jsonData,
            HttpServletResponse response) throws IOException {

        // Only decode and parse here - no business logic
        WebhookData webhookData = objectMapper.readValue(
                URLDecoder.decode(jsonData, StandardCharsets.UTF_8),
                WebhookData.class);

        paymentService.handlePaymentWebhook(webhookData, response);
    }


}
