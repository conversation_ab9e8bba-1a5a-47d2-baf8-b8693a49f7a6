package com.ssbms.backOfficeService.controller;

import com.ssbms.backOfficeService.model.dto.apiKeyDTO.ApiKeyListResponse;
import com.ssbms.backOfficeService.model.dto.apiKeyDTO.ApiKeyRequest;
import com.ssbms.backOfficeService.model.dto.apiKeyDTO.ApiKeyResponse;
import com.ssbms.backOfficeService.service.ApiKeyService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/projects/{project_id}/api-keys")
@RequiredArgsConstructor
public class ApiKeyController {

    private final ApiKeyService apiKeyService;

    // Authenticated endpoints (require JWT)
    @PostMapping
    public ResponseEntity<ApiKeyResponse> createApiKey(@PathVariable("project_id") UUID projectId, @Valid @RequestBody ApiKeyRequest apiKeyRequest) {
        ApiKeyResponse apiKeyDTO = apiKeyService.createApiKey(projectId, apiKeyRequest);
        return ResponseEntity.status(HttpStatus.CREATED).body(apiKeyDTO);
    }

    @GetMapping
    public ResponseEntity<List<ApiKeyListResponse>> getAllApiKeys(@PathVariable("project_id") UUID projectId) {
        List<ApiKeyListResponse> apiKeys = apiKeyService.getAllApiKeysForProject(projectId);
        return ResponseEntity.ok(apiKeys);
    }

    // ********************** public endpoints *****************************
    @PostMapping("/public")
    public ResponseEntity<ApiKeyResponse> createApiKeyPublic(@PathVariable("project_id") UUID projectId, @RequestBody ApiKeyRequest apiKeyRequest) {
        ApiKeyResponse apiKeyDTO = apiKeyService.createApiKeyPublic(projectId, apiKeyRequest);
        return ResponseEntity.status(HttpStatus.CREATED).body(apiKeyDTO);
    }

    @GetMapping("/public")
    public ResponseEntity<List<ApiKeyListResponse>> getAllApiKeysForProject(@PathVariable("project_id") UUID projectId)  {
        List<ApiKeyListResponse> apiKeys = apiKeyService.getAllApiKeysForProjectPublic(projectId);
        return ResponseEntity.ok(apiKeys);
    }

    // Revoke a specific API key (public)
    @PostMapping("/public/{api_key_id}/revoke")
    public ResponseEntity<String> revokeApiKey(@PathVariable("project_id") UUID projectId, @PathVariable("api_key_id") UUID apiKeyId) {
        apiKeyService.revokeApiKeyPublic(projectId, apiKeyId);
        return ResponseEntity.ok("API key revoked successfully");
    }
}