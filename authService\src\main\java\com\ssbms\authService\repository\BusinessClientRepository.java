package com.ssbms.authService.repository;

import com.ssbms.authService.model.entity.BusinessClient;
import jakarta.validation.constraints.NotBlank;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;
import java.util.UUID;

public interface BusinessClientRepository extends JpaRepository<BusinessClient, UUID> {

    Optional<BusinessClient> findByKeycloakUserId(String keycloakUserId);
}