package com.ssbms.paymentService.service.impl;

import com.ssbms.paymentService.model.entity.PaymentTransaction;
import com.ssbms.paymentService.repository.PaymentTransactionRepository;
import com.ssbms.paymentService.service.TransactionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class TransactionServiceImpl implements TransactionService {

    private final PaymentTransactionRepository paymentTransactionRepository;

    @Override
    public List<PaymentTransaction> getTransactionsByEmail(String email) {
        log.info("Fetching transactions for user with email: {}", email);
        List<PaymentTransaction> transactions = paymentTransactionRepository.findAllByEndUserEmail(email);

        if (transactions.isEmpty()) {
            log.warn("No transactions found for user with email: {}", email);
        } else {
            log.info("Found {} transactions for user with email: {}", transactions.size(), email);
        }

        return transactions;
    }
}
