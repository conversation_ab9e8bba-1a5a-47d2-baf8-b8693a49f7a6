# image: openjdk:23-slim

# stages:
#   - build
#   - package
#   - deploy

# variables:
#   MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository"

# cache:
#   paths:
#     - .m2/repository/

# # Build all services
# build:
#   stage: build
#   before_script:
#     - apt-get update
#     - apt-get install -y maven
#   script:
#     - cd authService && mvn clean package -DskipTests
#     - cd ../backOfficeService && mvn clean package -DskipTests
#     - cd ../notificationService && mvn clean package -DskipTests
#     - cd ../subscriptionService && mvn clean package -DskipTests
#     - cd ../paymentService && mvn clean package -DskipTests
#     - cd ../schedulerService && mvn clean package -DskipTests
#   artifacts:
#     paths:
#       - authService/target/*.jar
#       - backOfficeService/target/*.jar
#       - notificationService/target/*.jar
#       - subscriptionService/target/*.jar
#       - paymentService/target/*.jar
#       - schedulerService/target/*.jar
#       - init.sql

# # Package all services
# package:
#   stage: package
#   image: docker:20.10.12
#   services:
#     - docker:20.10.12-dind
#   variables:
#     DOCKER_TLS_CERTDIR: "/certs"
#   script:
#     - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY

#     # Build and push auth service
#     - cd authService
#     - docker build -t $CI_REGISTRY_IMAGE/auth-service:$CI_COMMIT_SHA .
#     - docker push $CI_REGISTRY_IMAGE/auth-service:$CI_COMMIT_SHA
#     - cd ..

#     # Build and push backoffice service
#     - cd backOfficeService
#     - docker build -t $CI_REGISTRY_IMAGE/backoffice-service:$CI_COMMIT_SHA .
#     - docker push $CI_REGISTRY_IMAGE/backoffice-service:$CI_COMMIT_SHA
#     - cd ..

#     # Build and push notification service
#     - cd notificationService
#     - docker build -t $CI_REGISTRY_IMAGE/notification-service:$CI_COMMIT_SHA .
#     - docker push $CI_REGISTRY_IMAGE/notification-service:$CI_COMMIT_SHA
#     - cd ..

#     # Build and push subscription service
#     - cd subscriptionService
#     - docker build -t $CI_REGISTRY_IMAGE/subscription-service:$CI_COMMIT_SHA .
#     - docker push $CI_REGISTRY_IMAGE/subscription-service:$CI_COMMIT_SHA
#     - cd ..

#     # Build and push payment service
#     - cd paymentService
#     - docker build -t $CI_REGISTRY_IMAGE/payment-service:$CI_COMMIT_SHA .
#     - docker push $CI_REGISTRY_IMAGE/payment-service:$CI_COMMIT_SHA
#     - cd ..

#     # Build and push scheduler service
#     - cd schedulerService
#     - docker build -t $CI_REGISTRY_IMAGE/scheduler-service:$CI_COMMIT_SHA .
#     - docker push $CI_REGISTRY_IMAGE/scheduler-service:$CI_COMMIT_SHA
#     - cd ..

#   dependencies:
#     - build
#   only:
#     - main

# deploy:
#   stage: deploy
#   image: alpine:3.15
#   before_script:
#     - apk add --no-cache openssh-client bash sshpass openssl curl
#     - mkdir -p ~/.ssh
#     - echo "$DEPLOY_SERVER_HOST_KEY" >> ~/.ssh/known_hosts
#   script:
#     - echo "Deploying application to production server..."

#     # Test connectivity first
#     - echo "Testing Docker registry connectivity..."
#     - sshpass -p "$DEPLOY_SERVER_PASSWORD" ssh $DEPLOY_SERVER_USER@$DEPLOY_SERVER_HOST "curl -I --connect-timeout 10 https://registry-1.docker.io/v2/ || echo 'Registry connectivity issue detected'"

#     # Create deployment directory
#     - sshpass -p "$DEPLOY_SERVER_PASSWORD" ssh $DEPLOY_SERVER_USER@$DEPLOY_SERVER_HOST "mkdir -p /opt/saas2"

#     # Copy files
#     - sshpass -p "$DEPLOY_SERVER_PASSWORD" scp docker-compose-prod.yml $DEPLOY_SERVER_USER@$DEPLOY_SERVER_HOST:/opt/saas2/
#     - sshpass -p "$DEPLOY_SERVER_PASSWORD" scp init.sql $DEPLOY_SERVER_USER@$DEPLOY_SERVER_HOST:/opt/saas2/init.sql
#     - sshpass -p "$DEPLOY_SERVER_PASSWORD" scp -r ssl/ $DEPLOY_SERVER_USER@$DEPLOY_SERVER_HOST:/opt/saas2/

#     # Deploy with improved error handling and timeouts
#     - |
#       sshpass -p "$DEPLOY_SERVER_PASSWORD" ssh $DEPLOY_SERVER_USER@$DEPLOY_SERVER_HOST "
#       cd /opt/saas2 &&
      
#       # Create environment file
#       echo 'DB_PASSWORD=$DB_PASSWORD' > .env &&
#       echo 'KEYCLOAK_PASSWORD=$KEYCLOAK_PASSWORD' >> .env &&
#       echo 'SERVER_HOSTNAME=$DEPLOY_SERVER_HOST' >> .env &&
#       echo 'CI_REGISTRY_IMAGE=$CI_REGISTRY_IMAGE' >> .env &&
#       echo 'CI_COMMIT_SHA=$CI_COMMIT_SHA' >> .env &&
#       echo 'DOCKER_CLIENT_TIMEOUT=300' >> .env &&
#       echo 'COMPOSE_HTTP_TIMEOUT=300' >> .env &&
      
#       # Login to registry
#       echo 'Logging into Docker registry...' &&
#       docker login -u $CI_REGISTRY_USER -p '$CI_REGISTRY_PASSWORD' $CI_REGISTRY &&
      
#       # Pull images with retry logic
#       echo 'Pulling Docker images...' &&
#       for i in {1..3}; do
#         echo \"Attempt \$i to pull images...\" &&
#         docker-compose -f docker-compose-prod.yml pull && break ||
#         (echo \"Pull attempt \$i failed, waiting 30 seconds...\" && sleep 30)
#       done &&
      
#       # Initialize database if needed
#       echo 'Initializing database...' &&
#       docker-compose -f docker-compose-prod.yml up -d postgres &&
#       sleep 10 &&
#       docker exec postgres psql -U ssbms -d ssbms_db -f /opt/saas2/init.sql 2>/dev/null || echo 'Database already initialized' &&
      
#       # Start all services
#       echo 'Starting all services...' &&
#       docker-compose -f docker-compose-prod.yml up -d
#       "
#   retry:
#     max: 2
#     when:
#       - script_failure
#   timeout: 20m
#   only:
#     - main