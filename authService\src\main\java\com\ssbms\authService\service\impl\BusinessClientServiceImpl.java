package com.ssbms.authService.service.impl;

import com.ssbms.authService.exception.*;
import com.ssbms.authService.model.dto.*;
import com.ssbms.authService.model.entity.BusinessClient;
import com.ssbms.authService.repository.BusinessClientRepository;
import com.ssbms.authService.service.BusinessClientService;
import jakarta.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import org.keycloak.OAuth2Constants;
import org.keycloak.admin.client.KeycloakBuilder;
import org.keycloak.representations.AccessTokenResponse;
import org.springframework.beans.factory.annotation.Value;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.resource.UsersResource;
import org.keycloak.representations.idm.CredentialRepresentation;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class BusinessClientServiceImpl implements BusinessClientService {

    @Value("${app.keycloak.realm}") private String realm;
    @Value("${app.keycloak.serverUrl}") private String serverUrl;
    @Value("${app.keycloak.admin.clientId}") private String clientId;
    @Value("${app.keycloak.admin.clientSecret}") private String clientSecret;
    @Value("${logoutKeycloakUrl}") private String logoutKeycloakUrl;

    private final BusinessClientRepository businessClientRepository;
    private final Keycloak keycloak;

    @Override
    public LoginResponse loginBusinessClient(LoginRequest loginRequest) {
        // Validate input (email/password format)
        // validateLoginRequest(loginRequest);

        try {
            // Step 1: Fetch user from Keycloak by username (email)
            UserRepresentation user;
            try {
                user = getUserRepresentation(loginRequest.getUsername());
                System.out.println("user : " + user);
            } catch (NotFoundException e) {
                throw new NotFoundException("Invalid email or password");
            }

            // Step 2: Authenticate with Keycloak using password grant
            Keycloak keycloakInstance = buildKeycloakInstance(loginRequest);
            AccessTokenResponse tokenResponse = keycloakInstance.tokenManager().getAccessToken();

            // Step 3: Build user details from DB (or fallback to Keycloak-only data)
            UserResponse userResponse = buildUserResponse(user);
            // Step 4: Combine tokens + user info into a response
            LoginResult loginResult = buildLoginResult(tokenResponse, userResponse);

            return new LoginResponse("Login successful", 200, loginResult);

        } catch (NotFoundException e) {
            // Log and rethrow custom user not found message
            log.warn("User not found: {}", e.getMessage());
            throw e;

        } catch (Exception e) {
            log.error("Authentication error", e);
            String errorMessage = e.getMessage() != null ? e.getMessage().toLowerCase() : "";
            // Handle specific Keycloak errors (e.g., invalid credentials)
            if (errorMessage.contains("invalid_grant") ||
                    errorMessage.contains("invalid user credentials") ||
                    errorMessage.contains("unauthorized") ||
                    errorMessage.contains("401")) {
                throw new RequestValidationException("Invalid email or password");
            }
            throw e;
        }
    }

    @Override
    public RegisterResponse registerBusinessClient(RegisterRequest registerRequest) {
        // Validate input (email, password, name fields)
        // validateRegisterRequest(registerRequest);

        // Get Keycloak UsersResource to interact with user API
        UsersResource usersResource = getUsersResource();
        // Check if user already exists
        checkUserExists(usersResource, registerRequest.getUsername());

        // Step 1: Create Keycloak user representation
        UserRepresentation userRepresentation = createUserRepresentation(registerRequest);
        // Step 2: Send request to Keycloak
        Response response = usersResource.create(userRepresentation);

        // Validate Keycloak response (HTTP 201 = success)
        validateKeycloakResponse(response);

        // Step 3: Fetch the newly created user from Keycloak
        UserRepresentation createdUser = getUserRepresentation(registerRequest.getUsername());
        // Step 4: Save user to local DB
        createBusinessClient(registerRequest, createdUser);

        // Step 5: Send verification email
        sendVerificationEmail(createdUser.getId());

        return new RegisterResponse("User registered successfully", 201);
    }

    @Override
    public LogoutResponse logoutBusinessClient(String authHeader, String refreshToken) {
        // Validate Authorization header format ("Bearer <token>")
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            throw new RequestValidationException("Invalid authentication token");
        }

        // Extract token from header
        String token = authHeader.substring(7);

        try {
            RestTemplate restTemplate = new RestTemplate();

            // Refresh token is required for logout
            if (refreshToken == null || refreshToken.isEmpty()) {
                throw new RequestValidationException("Refresh token is required for logout");
            }

            // Prepare form data for Keycloak logout endpoint
            MultiValueMap<String, String> formParams = new LinkedMultiValueMap<>();
            formParams.add("refresh_token", refreshToken);
            formParams.add("client_id", clientId);
            formParams.add("client_secret", clientSecret);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(formParams, headers);

            // String logoutUrl = serverUrl + "/realms/" + realm + "/protocol/openid-connect/logout";

            // Send POST request to Keycloak
            ResponseEntity<String> response = restTemplate.postForEntity(logoutKeycloakUrl, requestEntity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                return new LogoutResponse("Logout successful", 200);
            } else {
                throw new KeycloakException("Failed to logout: " + response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("Logout error", e);
            throw new KeycloakException("Failed to logout: " + e.getMessage());
        }
    }

    @Override
    public void sendVerificationEmail(String userId) {
        getUsersResource().get(userId).sendVerifyEmail();
    }

    //    @Override
    //    @Transactional(readOnly = true)
    //    public UUID getBusinessClientIdByKeycloakUserId(String keycloakUserId) {
    //        return businessClientRepository.findByKeycloakUserId(keycloakUserId)
    //                .map(BusinessClient::getId)
    //                .orElseThrow(() -> new UserNotFoundException(
    //                        "Business Client not found for Keycloak User ID: " + keycloakUserId));
    //    }

    //    @Override
    //    public UserResource getUser(String userId) {
    //        return getUsersResource().get(userId);
    //    }

    //    private void validateLoginRequest(LoginRequest loginRequest) {
    //        if (loginRequest.getUsername() == null || loginRequest.getUsername().isEmpty()) {
    //            throw new RequestValidationException("Email is required");
    //        }
    //        if (loginRequest.getPassword() == null || loginRequest.getPassword().isEmpty()) {
    //            throw new RequestValidationException("Password is required");
    //        }
    //        if (!EmailValidator.isValidEmail(loginRequest.getUsername())) {
    //            throw new RequestValidationException("Invalid email format");
    //        }
    //    }
    //
    //    private void validateRegisterRequest(RegisterRequest registerRequest) {
    //        if (registerRequest.getUsername() == null || registerRequest.getUsername().isEmpty()) {
    //            throw new RequestValidationException("Email is required");
    //        }
    //        if (registerRequest.getPassword() == null || registerRequest.getPassword().isEmpty()) {
    //            throw new RequestValidationException("Password is required");
    //        }
    //        if (registerRequest.getFirstName() == null || registerRequest.getFirstName().isEmpty()) {
    //            throw new RequestValidationException("First Name is required");
    //        }
    //        if (registerRequest.getLastName() == null || registerRequest.getLastName().isEmpty()) {
    //            throw new RequestValidationException("Last Name is required");
    //        }
    //        if (!EmailValidator.isValidEmail(registerRequest.getUsername())) {
    //            throw new RequestValidationException("Invalid email format");
    //        }
    //    }

    private Keycloak buildKeycloakInstance(LoginRequest loginRequest) {
        return KeycloakBuilder.builder()
                .serverUrl(serverUrl)
                .realm(realm)
                .clientId(clientId)
                .clientSecret(clientSecret)
                .username(loginRequest.getUsername())
                .password(loginRequest.getPassword())
                .grantType(OAuth2Constants.PASSWORD)
                .scope("openid profile email")
                .build();
    }

    private UserRepresentation getUserRepresentation(String username) {
        List<UserRepresentation> users = getUsersResource().searchByUsername(username, true);
        if (users.isEmpty()) {
            throw new NotFoundException("User not found");
        }
        return users.get(0);
    }

    private UserResponse buildUserResponse(UserRepresentation user) {
        Optional<BusinessClient> businessClient = businessClientRepository.findByKeycloakUserId(user.getId());

        return businessClient.map(this::mapToUserResponse)
                .orElseGet(() -> createDefaultUserResponse(user.getId()));
    }

    private UserResponse mapToUserResponse(BusinessClient businessClient) {
        return new UserResponse(
                businessClient.getId().toString(),
                businessClient.getPrimaryContactName(),
                businessClient.getContactEmail(),
                businessClient.getBusinessType(),
                businessClient.getIndustry(),
                businessClient.getTimezone(),
                businessClient.getLocale(),
                businessClient.getVerificationStatus()
        );
    }

    private UserResponse createDefaultUserResponse(String keycloakUserId) {
        return new UserResponse(
                keycloakUserId,
                null,
                null,
                null,
                null,
                "UTC",
                "en_US",
                "PENDING"
        );
    }

    private LoginResult buildLoginResult(AccessTokenResponse tokenResponse, UserResponse userResponse) {
        return new LoginResult(
                tokenResponse.getToken(),
                tokenResponse.getRefreshToken(),
                tokenResponse.getExpiresIn(),
                tokenResponse.getTokenType(),
                userResponse
        );
    }

    private void checkUserExists(UsersResource usersResource, String username) {
        if (!usersResource.searchByUsername(username, true).isEmpty()) {
            throw new AlreadyExistsException("This email is already in use");
        }
    }

    private UserRepresentation createUserRepresentation(RegisterRequest registerRequest) {
        UserRepresentation user = new UserRepresentation();
        user.setEnabled(true);
        user.setFirstName(registerRequest.getFirstName());
        user.setLastName(registerRequest.getLastName());
        user.setUsername(registerRequest.getUsername()); // ==> the email = username
        user.setEmail(registerRequest.getUsername());
        user.setEmailVerified(false);

        CredentialRepresentation credential = new CredentialRepresentation();
        credential.setValue(registerRequest.getPassword());
        credential.setType(CredentialRepresentation.PASSWORD);
        user.setCredentials(List.of(credential));

        return user;
    }

    private void validateKeycloakResponse(Response response) {
        if (response.getStatus() != 201) {
            String errorDetails = response.readEntity(String.class);
            throw new KeycloakException("Keycloak error: " + errorDetails);
        }
    }

    private void createBusinessClient(RegisterRequest registerRequest, UserRepresentation user) {
        BusinessClient businessClient = new BusinessClient();
        businessClient.setKeycloakUserId(user.getId());
        businessClient.setPrimaryContactName(registerRequest.getFirstName() + " " + registerRequest.getLastName());
        businessClient.setContactEmail(registerRequest.getUsername());

        businessClientRepository.save(businessClient);
    }

    private UsersResource getUsersResource() {
        return keycloak.realm(realm).users();
    }
}