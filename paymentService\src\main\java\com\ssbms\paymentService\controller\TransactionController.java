package com.ssbms.paymentService.controller;

import com.ssbms.paymentService.model.dto.transaction.TransactionMapper;
import com.ssbms.paymentService.model.dto.transaction.TransactionResponse;
import com.ssbms.paymentService.model.entity.PaymentTransaction;
import com.ssbms.paymentService.service.TransactionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/payment/transactions")
@RequiredArgsConstructor
@Slf4j
public class TransactionController {

    private final TransactionService transactionService;
    private final TransactionMapper transactionMapper;

    @GetMapping("/by-email/{email}")
    public ResponseEntity<List<TransactionResponse>> getTransactionsByEmail(@PathVariable String email) {
        log.info("Received request to get transactions for user with email: {}", email);

        List<PaymentTransaction> transactions = transactionService.getTransactionsByEmail(email);
        List<TransactionResponse> responses = transactionMapper.toDtoList(transactions);
        return ResponseEntity.ok(responses);
    }

}
