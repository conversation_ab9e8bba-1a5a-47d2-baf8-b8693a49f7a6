package com.ssbms.backOfficeService.service.impl;

import com.ssbms.backOfficeService.exception.*;
import com.ssbms.backOfficeService.model.dto.apiKeyDTO.ApiKeyListResponse;
import com.ssbms.backOfficeService.model.dto.apiKeyDTO.ApiKeyRequest;
import com.ssbms.backOfficeService.model.dto.apiKeyDTO.ApiKeyResponse;
import com.ssbms.backOfficeService.model.entity.ApiKey;
import com.ssbms.backOfficeService.model.entity.Project;
import com.ssbms.backOfficeService.repository.ApiKeyRepository;
import com.ssbms.backOfficeService.repository.ProjectRepository;
import com.ssbms.backOfficeService.service.ApiKeyService;
import com.ssbms.backOfficeService.utils.ApiKeyGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ApiKeyServiceImpl implements ApiKeyService {

    private final ProjectRepository projectRepository;
    private final ApiKeyRepository apiKeyRepository;
    private final PasswordEncoder passwordEncoder;

    @Override
    @Transactional
    public ApiKeyResponse createApiKey(UUID projectId, ApiKeyRequest apiKeyRequest) {
        //Ensures the authenticated user owns the project.
        String keycloakUserId = SecurityContextHolder.getContext().getAuthentication().getName();
        Project project = projectRepository.findByIdWithBusinessClient(projectId)
                .orElseThrow(() -> new NotFoundException("Project not found"));

        if (!project.getBusinessClient().getKeycloakUserId().equals(keycloakUserId)) {
            throw new UnauthorizedException("You don't have permission for this project");
        }

        try {
            String prefix = ApiKeyGenerator.generatePrefix();
            String mainKey = ApiKeyGenerator.generateMainKey();
            String fullKey = prefix + mainKey;
            String hashedKey = passwordEncoder.encode(fullKey); // Hashed version

            // Stores the hashed key, prefix, and truncated key (for display).
            ApiKey apiKey = new ApiKey();
            apiKey.setProject(project);
            apiKey.setKeyName(apiKeyRequest.getKeyName());
            apiKey.setApiKey(mainKey.substring(0, 6));
            apiKey.setPublicApiKey(mainKey);
            log.info("mainKey = {}", mainKey);
            apiKey.setKeyPrefix(prefix);
            apiKey.setApiKeyHash(hashedKey);
            apiKey.setActive(true);
            apiKeyRepository.save(apiKey);

            return new ApiKeyResponse(
                    mainKey,
                    "API key created successfully. Please store it securely as it won't be shown again.",
                    201
            );
        } catch (Exception e) {
            log.error("Error generating API key", e);
            throw new CreationException("Failed to generate API key");
        }
    }

    @Override
    public List<ApiKeyListResponse> getAllApiKeysForProject(UUID projectId) {

        // Validates the user has access to the project.
        String keycloakUserId = SecurityContextHolder.getContext().getAuthentication().getName();
        Project project = projectRepository.findByIdWithBusinessClient(projectId)
                .orElseThrow(() -> new NotFoundException("Project not found"));

        if (!project.getBusinessClient().getKeycloakUserId().equals(keycloakUserId)) {
            throw new UnauthorizedException("You don't have permission for this project");
        }

        List<ApiKey> apiKeysList = apiKeyRepository.findByProjectId(projectId);

        if (apiKeysList.isEmpty()) {
            throw new NotFoundException("No Api keys, add one !");
        }

        return apiKeysList.stream()
                .map(apiKey -> new ApiKeyListResponse(
                        apiKey.getId(),
                        apiKey.getKeyName(),
                        apiKey.getApiKey(),
                        apiKey.getKeyPrefix(),
                        apiKey.isActive(),
                        apiKey.getCreatedAt(),
                        apiKey.getLastUsedAt(),
                        apiKey.getRevokedAt(),
                        apiKey.getExpiresAt(),
                        apiKey.getUsageCount()
                ))
                .collect(Collectors.toList());
    }

    @Override
    public UUID extractProjectId(String userProvidedKey) {
        // Checks if the key length is 40 characters.
        if (userProvidedKey == null || userProvidedKey.length() != 40) {
            throw new RequestException("Invalid API key format");
        }

        // Get all active API keys
        List<ApiKey> allActiveKeys = apiKeyRepository.findByIsActiveTrue();

        for (ApiKey storedKey : allActiveKeys) {
            try {
                // Reconstruct full key by combining stored prefix + user's key
                String fullKey = storedKey.getKeyPrefix() + userProvidedKey;

                if (passwordEncoder.matches(fullKey, storedKey.getApiKeyHash())) {
                    // Check expiration
                    if (storedKey.getExpiresAt() != null && storedKey.getExpiresAt().isBefore(Instant.now())) {
                        throw new UnauthorizedException("API key is expired");
                    }

                    // Update usage stats
                    storedKey.setLastUsedAt(Instant.now());
                    storedKey.setUsageCount(storedKey.getUsageCount() + 1);
                    apiKeyRepository.save(storedKey);

                    return storedKey.getProject().getId();
                }
            } catch (Exception e) {
                log.debug("Key validation failed for key ID: {}", storedKey.getId(), e);
            }
        }

        throw new RequestException("Invalid API key");
    }

    @Override
    @Transactional
    public void revokeApiKey(UUID apiKeyId) {
        String keycloakUserId = SecurityContextHolder.getContext().getAuthentication().getName();

        ApiKey apiKey = apiKeyRepository.findByIdWithProject(apiKeyId)
                .orElseThrow(() -> new RequestException("API key not found"));

        if (!apiKey.getProject().getBusinessClient().getKeycloakUserId().equals(keycloakUserId)) {
            throw new UnauthorizedException("You don't have permission to revoke this API key");
        }

        apiKey.setActive(false);
        apiKey.setRevokedAt(Instant.now());
        apiKeyRepository.save(apiKey);
    }

    // ********************** Public API Key Endpoints *****************************
    @Override
    @Transactional
    public ApiKeyResponse createApiKeyPublic(UUID projectId, ApiKeyRequest apiKeyRequest) {
        // Validate inputs
        if (apiKeyRequest.getKeyName() == null || apiKeyRequest.getKeyName().isEmpty()) {
            throw new RequestException("Key name is required");
        }

        // Verify project exists
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new NotFoundException("Project not found"));

        try {
            String prefix = ApiKeyGenerator.generatePrefix();
            String mainKey = ApiKeyGenerator.generateMainKey();
            String fullKey = prefix + mainKey;
            String hashedKey = passwordEncoder.encode(fullKey); // Hashed version

            // Stores the hashed key, prefix, and truncated key (for display).
            ApiKey apiKey = new ApiKey();
            apiKey.setProject(project);
            apiKey.setKeyName(apiKeyRequest.getKeyName());
            apiKey.setApiKey(mainKey.substring(0, 6));
            apiKey.setPublicApiKey(mainKey);
            log.info("mainKey = {}", mainKey);
            apiKey.setKeyPrefix(prefix);
            apiKey.setApiKeyHash(hashedKey);
            apiKey.setActive(true);
            apiKeyRepository.save(apiKey);

            return new ApiKeyResponse(
                    mainKey,
                    "API key created successfully. Please store it securely as it won't be shown again.",
                    201
            );
        } catch (Exception e) {
            log.error("Error generating API key", e);
            throw new CreationException("Failed to generate API key");
        }
    }

    @Override
    public List<ApiKeyListResponse> getAllApiKeysForProjectPublic(UUID projectId) {
        // Verify project exists (optional check)
        if (!projectRepository.existsById(projectId)) {
            throw new NotFoundException("Project not found with ID: " + projectId);
        }

        return apiKeyRepository.findByProjectId(projectId).stream()
                .map(this::convertToApiKeyListResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void revokeApiKeyPublic(UUID projectId, UUID apiKeyId) {
        ApiKey apiKey = apiKeyRepository.findByIdAndProjectId(apiKeyId, projectId)
                .orElseThrow(() -> new RequestException(
                        "API key not found or doesn't belong to the specified project"));

        apiKey.setActive(false);
        apiKey.setRevokedAt(Instant.now());
        apiKeyRepository.save(apiKey);
    }

    private ApiKeyListResponse convertToApiKeyListResponse(ApiKey apiKey) {
        return new ApiKeyListResponse(
                apiKey.getId(),
                apiKey.getKeyName(),
                apiKey.getApiKey(),
                apiKey.getKeyPrefix(),
                apiKey.isActive(),
                apiKey.getCreatedAt(),
                apiKey.getLastUsedAt(),
                apiKey.getRevokedAt(),
                apiKey.getExpiresAt(),
                apiKey.getUsageCount()
        );
    }
}

