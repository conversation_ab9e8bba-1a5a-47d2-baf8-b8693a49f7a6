package com.ssbms.authService.config;

import org.springframework.core.convert.converter.Converter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtClaimNames;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Component;

import java.util.Collections;

@Component
public class JwtAuthConverter implements Converter<Jwt, AbstractAuthenticationToken> {

    @Override
    public AbstractAuthenticationToken convert(Jwt jwt) {
        String principalClaimValue = getPrincipalClaimName(jwt); // Extract the `sub` claim (Keycloak user ID) from the JWT

        return new JwtAuthenticationToken(jwt, Collections.emptyList(), principalClaimValue); // Create a JwtAuthenticationToken with no authorities (roles)
    }

    private String getPrincipalClaimName(Jwt jwt) {
        return jwt.getClaim(JwtClaimNames.SUB); // Return the `sub` claim (Keycloak user ID)
    }
}
