package com.ssbms.subscriptionService.client;

import com.ssbms.subscriptionService.exception.PaymentProcessingException;
import com.ssbms.subscriptionService.model.dto.redirect.RedirectRequest;
import com.ssbms.subscriptionService.model.dto.redirect.RedirectResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class PaymentServiceRestClient {

    private final RestTemplate restTemplate;

    @Value("${payment.service.url}")
    private String paymentServiceUrl;

    public RedirectResponse processPayment(RedirectRequest request) {
        try {
            log.debug("Creating payment request DTO to avoid circular references");

            Map<String, Object> paymentRequestMap = new HashMap<>();
            paymentRequestMap.put("currency", request.getCurrency());
            paymentRequestMap.put("itemid", request.getItemid());
            paymentRequestMap.put("merchantid", request.getMerchantid());
            paymentRequestMap.put("amount", request.getAmount());
            paymentRequestMap.put("merchantname", request.getMerchantname());
            paymentRequestMap.put("websitename", request.getWebsitename());
            paymentRequestMap.put("websiteid", request.getWebsiteid());
            paymentRequestMap.put("callbackurl", request.getCallbackurl());
            paymentRequestMap.put("fname", request.getFname());
            paymentRequestMap.put("lname", request.getLname());
            paymentRequestMap.put("email", request.getEmail());
            paymentRequestMap.put("country", request.getCountry());
            paymentRequestMap.put("city", request.getCity());
            paymentRequestMap.put("state", request.getState());
            paymentRequestMap.put("zipcode", request.getZipcode());
            paymentRequestMap.put("address", request.getAddress());
            paymentRequestMap.put("phone", request.getPhone());
            paymentRequestMap.put("id_client", request.getIdClient());
            paymentRequestMap.put("token", request.getToken());

            // If using token-based payment
            if (request.getToken() != null) {
                paymentRequestMap.put("token", request.getToken());
            }

            log.debug("Sending simplified payment request to: {}/process", paymentServiceUrl);

            System.out.println("payment Request sent to payment service : " + paymentRequestMap);

            // Use postForObject with the simplified map to avoid circular references
            return restTemplate.postForObject(
                    paymentServiceUrl + "/redirectUrl",
                    paymentRequestMap,
                    RedirectResponse.class
            );
        } catch (HttpClientErrorException e) {
            log.error("Payment service HTTP error: {} - {}", e.getStatusCode(), e.getMessage());
            throw new PaymentProcessingException("Payment service error: " + e.getStatusCode() + " " + e.getMessage());
        } catch (RestClientException e) {
            log.error("Failed to call payment service: {} ----- {}", paymentServiceUrl, e.getMessage());
            throw new PaymentProcessingException("Failed to call payment service: " + e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error during payment processing: ", e);
            throw new PaymentProcessingException("Unexpected error during payment processing: " + e.getMessage());
        }
    }
}
