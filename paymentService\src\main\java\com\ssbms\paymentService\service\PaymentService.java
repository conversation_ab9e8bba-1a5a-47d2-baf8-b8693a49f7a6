package com.ssbms.paymentService.service;

import com.ssbms.paymentService.model.dto.payment.RedirectRequest;
import com.ssbms.paymentService.model.dto.payment.RedirectResponse;
import com.ssbms.paymentService.model.dto.webhook.WebhookData;
import com.ssbms.paymentService.model.entity.PaymentTransaction;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;

import java.io.IOException;
import java.util.List;

public interface PaymentService {

    RedirectResponse redirectToCheckout(RedirectRequest paymentRequest);

    // void handlePaymentWebhook(WebhookData webhookRequest);

    WebhookData handlePaymentScheduler(@Valid RedirectRequest paymentRequest);

    void handlePaymentWebhook(WebhookData webhookData, HttpServletResponse response) throws IOException;
}
