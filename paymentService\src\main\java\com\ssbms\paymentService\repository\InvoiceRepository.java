package com.ssbms.paymentService.repository;

import com.ssbms.paymentService.model.entity.Invoice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface InvoiceRepository extends JpaRepository<Invoice, UUID> {

    List<Invoice> findByTransactionPaymentMethodEndUserEmail(String email);

    Optional<Invoice> findByInvoiceNumber(String invoiceNumber);
}
