package com.ssbms.notificationService.repository;

import com.ssbms.notificationService.model.entity.PaymentTransaction;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;
import java.util.UUID;

public interface PaymentTransactionRepository extends JpaRepository<PaymentTransaction, UUID> {

    @EntityGraph(attributePaths = {
            "paymentMethod",
            "paymentMethod.endUser.endUserSubscriptions",
            "paymentMethod.endUser.project"
    })
    @Query("SELECT t FROM PaymentTransaction t " +
            "LEFT JOIN FETCH t.paymentMethod pm " +
            "LEFT JOIN FETCH pm.endUser eu " +
            "LEFT JOIN FETCH eu.endUserSubscriptions eus " +
            "LEFT JOIN FETCH eu.project p " +
            "WHERE t.id = :transactionId")
    Optional<PaymentTransaction> findTransactionForNotification(@Param("transactionId") UUID transactionId);
}
