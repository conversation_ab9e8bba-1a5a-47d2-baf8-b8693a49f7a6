# Application configuration
spring:
  application:
    name: payment-service

  # PostgreSQL database configuration
  datasource:
    url: ${SPRING_DATASOURCE_URL:*****************************************}
    username: ssbms
    password: ssbms1234
    driver-class-name: org.postgresql.Driver

  # JPA (Hibernate) configuration
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    properties:
      hibernate:
        allow-circular-references: true

  # Kafka configuration
  kafka:
    bootstrap-servers: ${SPRING_KAFKA_BOOTSTRAP_SERVERS:localhost:2025}

    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer

server:
  port: ${SERVER_PORT:1005}

# Swagger (OpenAPI) configuration
springdoc:
  swagger-ui:
    path: /swagger-ui.html
  api-docs:
    path: /api-docs

# Logging configuration
logging:
  level:
    org:
      springframework:
        security: DEBUG
        web.client.RestTemplate: DEBUG
    org.apache.tomcat.util.net: DEBUG
    org.springframework.boot.web.embedded.tomcat: DEBUG
    org.springframework.web.reactive.function.client: DEBUG

payment:
  wallet:
    api:
      url: http://**************:2030/api/business-transactions/linkpayment

invoice:
  storage:
    path: ${INVOICE_STORAGE_PATH:./invoices}