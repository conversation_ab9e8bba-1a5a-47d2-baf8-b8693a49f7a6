package com.ssbms.paymentService.repository;

import com.ssbms.paymentService.model.entity.BillingCycle;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface BillingCycleRepository extends JpaRepository<BillingCycle, UUID> {

    Optional<BillingCycle> findFirstBySubscriptionIdOrderByBillingDateDesc(UUID subscriptionId);

    List<BillingCycle> findByBillingDateBetweenOrStatus(LocalDateTime now, LocalDateTime nextWindow, String status);

    List<BillingCycle> findByStatus(String status);

    List<BillingCycle> findBySubscriptionIdAndBillingDateGreaterThanEqual(UUID subscriptionId, LocalDateTime billingDate);

    List<BillingCycle> findBySubscriptionIdAndBillingDateBetween(UUID id, LocalDateTime localDateTime, LocalDateTime localDateTime1);

    List<BillingCycle> findByStatusAndBillingDateBetween(String status, LocalDateTime start, LocalDateTime end);

    BillingCycle findByIdAndBillingDate(UUID billingCycleId, LocalDateTime billingDate);

    Optional<BillingCycle> findBySubscriptionIdAndStatus(UUID subscriptionId, String status);
}
