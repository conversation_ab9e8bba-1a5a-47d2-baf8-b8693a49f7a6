package com.ssbms.paymentService.model.dto.invoice;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceResponse {

    private UUID id;
    private String invoiceNumber;
    private LocalDateTime createdAt;
    private String status;
    private Double amount;
    private String currency;
    private String customerName;
    private String customerEmail;
}
