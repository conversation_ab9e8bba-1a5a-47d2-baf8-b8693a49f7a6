# Application configuration
spring:
  application:
    name: scheduler-service

  # PostgreSQL database configuration
  datasource:
    url: ${SPRING_DATASOURCE_URL:*****************************************}
    username: ssbms
    password: ssbms1234
    driver-class-name: org.postgresql.Driver

  # JPA (Hibernate) configuration
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    properties:
      hibernate:
        allow-circular-references: true

  # Kafka configuration
  kafka:
    bootstrap-servers: ${SPRING_KAFKA_BOOTSTRAP_SERVERS:localhost:3025}

    consumer:
      group-id: billing-group
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        spring.json.trusted.packages: "*"
        spring.json.value.default.type: com.ssbms.schedulerService.model.dto.BillingCycleRequest

    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer

    streams:
      application-id: subscription-streams
      properties:
        default.key.serde: org.apache.kafka.common.serialization.Serdes$UUIDSerde
        default.value.serde: org.springframework.kafka.support.serializer.JsonSerde
        spring.json.value.default.type: com.ssbms.schedulerService.model.dto.BillingCycleRequest
        spring.json.trusted.packages: "*"

  jackson:
    serialization:
      FAIL_ON_EMPTY_BEANS: false

# Swagger (OpenAPI) configuration
springdoc:
  swagger-ui:
    path: /swagger-ui.html
  api-docs:
    path: /api-docs

# Logging configuration
logging:
  level:
    org:
      springframework:
        security: DEBUG
        web.client.RestTemplate: DEBUG
    org.apache.tomcat.util.net: DEBUG
    org.springframework.boot.web.embedded.tomcat: DEBUG
    org.springframework.web.reactive.function.client: DEBUG

payment:
  service:
    # url: http://paymentservice:8091/payments
    url: http://localhost:1005/payments

server:
  port: ${SERVER_PORT:1006}