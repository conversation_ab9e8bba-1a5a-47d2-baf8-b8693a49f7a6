package com.ssbms.paymentService.kafka;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class NotificationProducer {

    private final KafkaTemplate<String, String> kafkaTemplate;
    private static final String TOPIC = "notifications";

    public void sendWelcomeEmail(UUID transactionId) {
        try {
            String jsonPayload = String.format(
                    "{\"type\":\"WELCOME_EMAIL\",\"transactionId\":\"%s\"}",
                    transactionId
            );

            kafkaTemplate.send(TOPIC, jsonPayload);
            log.info("Sent welcome email notification to Kafka: {}", jsonPayload);
        } catch (Exception e) {
            log.error("Failed to send welcome email notification", e);
        }
    }

    public void sendPaymentConfirmation(UUID transactionId) {
        try {
            // Create JSON string manually
            String jsonPayload = String.format(
                    "{\"type\":\"PAYMENT_CONFIRMATION\",\"transactionId\":\"%s\"}",
                    transactionId
            );

            kafkaTemplate.send(TOPIC, jsonPayload);
            log.info("Sent payment confirmation notification to Kafka: {}", jsonPayload);
        } catch (Exception e) {
            log.error("Failed to send payment confirmation notification", e);
        }
    }
}