package com.ssbms.paymentService.model.dto.transaction;

import com.ssbms.paymentService.model.entity.PaymentTransaction;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class TransactionMapper {

    public TransactionResponse toDto(PaymentTransaction transaction) {
        return TransactionResponse.builder()
                .id(transaction.getId())
                .reference(transaction.getReference())
                .transactionReference(transaction.getTransactionReference())
                .amount(transaction.getAmount())
                .currency(transaction.getCurrency())
                .status(transaction.getStatus())
                .paymentProcessor(transaction.getPaymentProcessor())
                .cardLastFour(transaction.getCardLastFour())
                .processedAt(transaction.getProcessedAt())
                .createdAt(transaction.getCreatedAt())
                .externalTransactionId(transaction.getExternalTransactionId())
                .build();
    }

    public List<TransactionResponse> toDtoList(List<PaymentTransaction> transactions) {
        return transactions.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }
}
